<?php

namespace App\Http\Controllers\Fleet;

use Carbon\Carbon;
use App\Http\Helper;
use App\Models\User;
use App\Workshop\Workshop;
use App\Models\Fleet\Asset;
use App\FleetMaster\Vehicle;
use App\Models\Fleet\Driver;
use Illuminate\Http\Request;
use App\Models\Fleet\Company;
use App\Models\Fleet\Question;
use App\Models\Fleet\AssetDetail;
use App\Models\Fleet\QuestionMark;
use Illuminate\Support\Facades\DB;
use App\Models\Fleet\StatusVehicle;
use Illuminate\Support\Facades\Log;
use App\FleetMaster\VehicleKmActual;
use App\Http\Controllers\Controller;
use App\Models\Fleet\AnswerQuestion;
use Illuminate\Support\Facades\Auth;
use App\Models\Fleet\AssetSecurityLog;
use App\Models\Fleet\CategoryQuestion;
use App\Models\Fleet\LocationChecklist;
use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\AnswerQuestionDetail;
use App\Models\Fleet\RiwayatAnswerQuestionUser;

class ChecklistVehicleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $auth = Auth::user()->role;
        $data = AnswerQuestionUser::where('user_id', Auth::user()->id)
        ->with('answerQuestionDetail', 'vehicle', 'riwayatTindakan.approveBy')
        ->where('slug', 'checklist-vehicle')
        ->where('vehicle_id', '!=', null)
        ->when(($request->start_date ?? false) || ($request->to_date ?? false), function ($query) use ($request) {
            return $query->whereDate('created_at', '>=', date('Y-m-d', strtotime($request->start_date)))
                        ->whereDate('created_at', '<=',  date('Y-m-d', strtotime($request->to_date)));
        })
        ->where(function($query) use($request){
            if ($request->get('status_vehicle_id')) {
                if ($request->get('status_vehicle_id') == 2) {
                    $query->whereIn('status_vehicle_id',[2,1]);
                }else{
                    $query->where('status_vehicle_id',$request->get('status_vehicle_id'));
                }
            }
        })
        ->orderBy('created_at', 'desc')
        ->get();
        foreach ($data as $key => $res) {
            $answerQuestionDetail = AnswerQuestionDetail::where('answer_question_user_id', $res->id)->get();
            $data[$key]['danger'] = false;
            foreach ($answerQuestionDetail as $cvalue) {
                if ($cvalue->answerQuestion->danger) {
                    $data[$key]['danger'] = true;
                }
            }

            if ($data[$key]['danger'] == false) {
                if (isset($res->statusVehicle->start)) {
                    $sv = $res->statusVehicle->start;
                    if ($res['total_point'] <= $sv) {
                        $data[$key]['ready'] = 0;
                    }else{
                        $data[$key]['ready'] = 1;
                    }
                }
                else{
                    $data[$key]['ready'] = 0;
                }
            }else if($data[$key]['danger'] == true){
                $data[$key]['status_vehicle'] = array('name' => 'red', 'color' => '#ff0000', 'danger' => true);
                $data[$key]['ready'] = 0;
            }

            if (is_null($data[$key]['status_vehicle']) || $data[$key]['status_vehicle'] == null || is_null($data[$key]['status_vehicle']['color'])) {
                $data[$key]['status_vehicle'] = array('name' => 'red', 'color' => '#ff0000', 'danger' => true);
            }
        }

        return response()->json(['data' => $data, 'user' => $auth]);
    }

    public function indexK3(Request $request)
    {
        try {
            $user_login = Auth::user();
            if ($user_login->role == 'Administrator') {
                $uid_location_same = User::pluck('id');
            }else{
                $uid_location_same = User::whereIn('location_id', json_decode(Auth::user()->allow_location_id))->pluck('id');
            }

            $data['k3'] = AnswerQuestionUser::whereIn('user_id', $uid_location_same)
            ->whereNotNull('status_vehicle_id')
            ->where('slug', 'checklist-vehicle')
            ->when($request->type ?? false , function ($query) use ($request) {
                return $query->where('type', $request->type);
            })
            ->when($request->status_vehicle_id ?? false, function ($query) use ($request) {
                if ($request->get('status_vehicle_id') == 2)
                    return $query->whereIn('status_vehicle_id',[2,1]);
                else
                    return $query->where('status_vehicle_id',$request->get('status_vehicle_id'));
            })
            ->when($request->company ?? false, function ($query) use ($request) {
                $company = Company::find($request->get('company'))->code;
                $user_id_company = User::where('company_id', $company)->pluck('id');
                return $query->whereIn('user_id', $user_id_company);
            })
            ->when(($request->start_date ?? false) || ($request->to_date ?? false), function ($query) use ($request) {
                return $query->whereDate('created_at', '>=', date('Y-m-d', strtotime($request->start_date)))
                            ->whereDate('created_at', '<=',  date('Y-m-d', strtotime($request->to_date)));
            })
            ->with(['user', 'statusVehicle', 'answerQuestionDetail', 'riwayatTindakan.approveBy'])
            ->orderBy('updated_at', 'desc')
            ->filter($request)
            ->paginate($request->paginate ?? 10);


            foreach ($data['k3'] as $key => $value) {
                $data['k3'][$key]['license_no'] = AssetDetail::where('asset_id', $value->asset_id)->where('attribute_code', 'nomor_polisi')->first()->value ?? null;
                $data['k3'][$key]['keputusan_koordinator'] = Workshop::where('answer_question_user_id', $value->id)->where('status_perbaikan_id', 6)->first() ?? null;
            }

            $data['jumlah_data'] = AnswerQuestionUser::whereNull('recheck_answer_question_user_id')
            ->where(function($query) use($request){
                if ($request->get('status_vehicle_id')) {
                    if ($request->get('status_vehicle_id') == 2) {
                        $query->whereIn('status_vehicle_id',[2,1]);
                    }else{
                        $query->where('status_vehicle_id',$request->get('status_vehicle_id'));
                    }
                }

                if ($request->get('location')) {
                    $query->where('location_checklist_id', $request->get('location'));
                }

                if ($request->get('company')) {
                    $company = Company::findOrFail($request->get('company'))->code;
                    $user_id_company = User::where('company_id', $company)->pluck('id');
                    $query->whereIn('user_id', $user_id_company);
                }

                if ($request->get('type')) {
                    $query->where('type', $request->get('type'));
                }
            })
            ->whereNotNull('status_vehicle_id')
            ->where('slug', 'checklist-vehicle')
            ->when(($request->start_date ?? false) || ($request->to_date ?? false), function ($query) use ($request) {
                return $query->whereDate('created_at', '>=', date('Y-m-d', strtotime($request->start_date)))
                            ->whereDate('created_at', '<=',  date('Y-m-d', strtotime($request->to_date)));
            })
            ->get()
            ->groupBy('asset_id')
            ->count();

            $data['jumlah_data_merah'] = AnswerQuestionUser::whereNull('recheck_answer_question_user_id')->where(function($query) use($request){
                if ($request->get('status_vehicle_id')) {
                    if ($request->get('status_vehicle_id') == 2) {
                        $query->whereIn('status_vehicle_id',[2,1]);
                    }else{
                        $query->where('status_vehicle_id',$request->get('status_vehicle_id'));
                    }
                }

                if ($request->get('location')) {
                    $query->where('location_checklist_id', $request->get('location'));
                }

                if ($request->get('company')) {
                    $company = Company::findOrFail($request->get('company'))->code;
                    $user_id_company = User::where('company_id', $company)->pluck('id');
                    $query->whereIn('user_id', $user_id_company);
                }

                if ($request->get('type')) {
                    $query->where('type', $request->get('type'));
                }
            })
            ->where('status_vehicle_id', 4)
            ->where('slug', 'checklist-vehicle')
            ->when(($request->start_date ?? false) || ($request->to_date ?? false), function ($query) use ($request) {
                return $query->whereDate('created_at', '>=', date('Y-m-d', strtotime($request->start_date)))
                            ->whereDate('created_at', '<=',  date('Y-m-d', strtotime($request->to_date)));
            })
            ->with(['user', 'statusVehicle', 'answerQuestionDetail'])
            ->orderBy('id', 'desc')
            ->count();

            $data['jumlah_data_hijau'] = AnswerQuestionUser::whereNull('recheck_answer_question_user_id')->where(function($query) use($request){
                if ($request->get('status_vehicle_id')) {
                    if ($request->get('status_vehicle_id') == 2) {
                        $query->whereIn('status_vehicle_id',[2,1]);
                    }else{
                        $query->where('status_vehicle_id',$request->get('status_vehicle_id'));
                    }
                }

                if ($request->get('location')) {
                    $query->where('location_checklist_id', $request->get('location'));
                }

                if ($request->get('company')) {
                    $company = Company::findOrFail($request->get('company'))->code;
                    $user_id_company = User::where('company_id', $company)->pluck('id');
                    $query->whereIn('user_id', $user_id_company);
                }

                if ($request->get('type')) {
                    $query->where('type', $request->get('type'));
                }
            })
            ->whereIn('status_vehicle_id', [2,1])
            ->where('slug', 'checklist-vehicle')
            ->with(['user', 'statusVehicle', 'answerQuestionDetail'])
            ->orderBy('id', 'desc')
            ->when(($request->start_date ?? false) || ($request->to_date ?? false), function ($query) use ($request) {
                return $query->whereDate('created_at', '>=', date('Y-m-d', strtotime($request->start_date)))
                            ->whereDate('created_at', '<=',  date('Y-m-d', strtotime($request->to_date)));
            })
            ->count();

            $data['jumlah_kendaraan'] = Asset::where('category_asset_id', 3)
            ->where(function($query) use($request){
                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
            })
            ->count();
            $data['storing'] = Asset::where('category_asset_id', 3)
            ->where(function($query) use($request){
                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
            })
            ->where('maintenance', 1)->count();

            $luar_kota = Asset::where('category_asset_id', 3)->where('trip', 1)
            ->where(function($query) use($request){
                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
            })
            ->pluck('id');
            $data['luar_kota'] = AnswerQuestionUser::whereNotNull('status_vehicle_id')
            ->where('slug', 'checklist-vehicle')
            ->whereIn('asset_id', $luar_kota)
            ->orderBy('id', 'desc')
            ->get()
            ->groupBy('asset_id')
            ->count();

            $asset_chek_1 = Asset::where('category_asset_id', 3)->whereNotNull('trip')
            ->where(function($query) use($request){
                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
            })
            ->pluck('id');
            $already_cheklist = AnswerQuestionUser::whereNotNull('status_vehicle_id')
            ->where('slug', 'checklist-vehicle')
            ->whereIn('asset_id', $asset_chek_1)
            ->orderBy('id', 'desc')
            ->get()
            ->groupBy('asset_id');

            $data['already_cheklist'] = 0;
            foreach ($already_cheklist as $key => $value) {
                $chek_1 = $value->first();
                if ($chek_1->type == 1) {
                    $data['already_cheklist'] ++;
                }
            }

            $data['dont_cheklist'] = $data['jumlah_kendaraan'] - $data['storing'] - $data['luar_kota'] - $data['already_cheklist'];

            $data['status_vehicle'] = StatusVehicle::get();
            return response()->json($data);

        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        try {
            $qrcode = $request->qrcode;
            $answer_question_user_id = $request->answer_question_user_id;
            $category_id = $request->category_id;

            $quest = AnswerQuestionUser::find($answer_question_user_id);
            if (!$quest) {
                return response()->json(['status' => false, 'messages' => 'terjadi kesalahan quest']);
            }

            if (!isset($quest->asset_id)) {
                return response()->json(['status' => false, 'messages' => 'terjadi kesalahan asset']);
            }

            $asset = $quest->asset->assetDetail->where('attribute_code', 'nomor_polisi')->first();
            $kendaraan['id'] = $asset->asset_id;
            $kendaraan['license_no'] = $asset->value;

            $age = now()->diffInYears(Auth::user()->birth);
            $type_kendaraan_trailer = Asset::whereId($quest->asset_id)->where('type_asset_id', 13)->first();
            $data = CategoryQuestion::where('slug', 'checklist-vehicle')->orderBy('id', 'asc')->with('question.answer')->get();
            if ($type_kendaraan_trailer) {
                $data = CategoryQuestion::whereNotIn('id', [17])->where('slug', 'checklist-vehicle')->orderBy('id', 'asc')->with('question.answer')->get();
            }

            foreach ($data as $key => $value) {
                $data[$key]['question'] = $value->question()->whereNull('age_range')->orWhere('age_range', '<', $age)->with('answer')->get();
                if($quest->sealed == 2){
                    $value->qr_code = ($value->qr_code == '') ? null:$value->qr_code ;
                }else{
                    if($value->qr_code == 'QR CEK KAROSERI')
                        {
                            $value->qr_code = '';
                        }else{
                            $value->qr_code = ($value->qr_code == '') ? null:$value->qr_code ;
                        }
                }

            }
            foreach ($data as $key => $value) {
                $data[$key]['danger'] = false;
                $data[$key]['status_category'] = 0;
                $data[$key]['point'] = 0;
                foreach ($value->question as $ckey => $cvalue) {
                    $answerQuestionDetail = AnswerQuestionDetail::with('answerQuestion')->where('answer_question_user_id', $answer_question_user_id)->where('question_id', $cvalue->id)->first();
                    if(isset($answerQuestionDetail->answerQuestion->danger)){
                        if ($answerQuestionDetail->answerQuestion->danger == 1) {
                            // $data[$key]['status'] = array('name' => 'red', 'color' => '#ff0000', 'status' => 'tidak lulus');
                            $data[$key]['danger'] = true;
                        }else{
                            // $data[$key]['status'] = array('name' => 'green', 'color' => '#00FF00', 'status' => 'lulus');
                        }
                    }else{
                        $data[$key]['status'] = array('name' => 'gray', 'color' => '#808080', 'status' => 'belum dikerjakan');
                    }
                    // foreach ($answerQuestionDetail->answerQuestion as $svalue) {
                    //     dd($svalue);
                    // }
                    // return $answerQuestionDetail->answerQuestion->danger;
                    if (isset($answerQuestionDetail->point)) {
                        $data[$key]['status_category'] = 1;
                    }
                    $data[$key]['point'] += $answerQuestionDetail->point ?? 0;
                    // if (isset($answerQuestionDetail->answerQuestion->danger)) {
                    //     $data[$key]['danger'] = true;
                    // }
                }

                if ($data[$key]['status_category'] == 0) {
                    $data[$key]['status'] = array('name' => 'gray', 'color' => '#808080', 'status' => 'belum dikerjakan');
                }else {
                    if ($data[$key]['danger'] == true) {
                        $data[$key]['status'] = array('name' => 'red', 'color' => '#ff0000', 'status' => 'tidak lulus');
                    }else{
                        $data[$key]['status'] = array('name' => 'green', 'color' => '#00FF00', 'status' => 'lulus');
                    }
                }



                // if ($data[$key]['danger'] == false) {
                //     if ($data[$key]['status_category'] == 0) {
                //         $data[$key]['status'] = array('name' => 'gray', 'color' => '#808080', 'status' => 'belum dikerjakan');
                //     }else{
                //         $data[$key]['status'] = array('name' => 'gray', 'color' => '#808080', 'status' => 'belum dikerjakan');
                //     }
                // }else if($data[$key]['danger'] == true){
                //     $data[$key]['status'] = array('name' => 'gray', 'color' => '#808080', 'status' => 'belum dikerjakan');
                // }
            }
            // return $data;
            if ($qrcode) {
                $data = CategoryQuestion::where('slug', 'checklist-vehicle')->where('qr_code', $qrcode)->orderBy('id', 'asc')->with('question.answer')->get();
                return response()->json($data);
            }else if($category_id){
                $data = CategoryQuestion::where('id', $category_id)->orderBy('id', 'asc')->with('question.answer')->get();
                return response()->json($data);
            }

            return response()->json(['kendaraan' => $kendaraan , 'category' => $data, 'sealed' => $quest->sealed]);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'messages' => $th]);
        }
    }

    public function cekAnswer($id)
    {
        $data = AnswerQuestion::findOrFail($id);

        return response()->json(['success' => true, 'data' => $data]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            Log::info($data);
            // dd($data);
            $answerQuestionUser['vehicle_id'] = $data[0]['kendaraan_id'];
            $answerQuestionUser['status'] = 0;
            $answerQuestionUser['user_id'] = Auth::user()->id;
            $answerQuestionUser['slug'] = 'checklist-vehicle';
            $answerUser = AnswerQuestionUser::create($answerQuestionUser);

            $accompanied_foto = false;
            $question_accompanied_foto = array();
            foreach ($data as $key => $value) {
                $question = Question::findOrFail($value['question_id']);
                if ($question->accompanied_photo == 1 && !(isset($value['image']))) {
                    $accompanied_foto = true;
                    $question_accompanied_foto[$key] = $value['question_id'];
                }
            }

            if ($accompanied_foto == false) {
                $CategoryQuestion = array();
                foreach ($data as $key => $res) {
                    $question = Question::findOrFail($res['question_id']);
                    $answer = AnswerQuestion::findOrFail($res['answer_id']);
                    $res['question'] = $question->question;
                    $res['answer'] = $answer->answer;
                    $res['point'] = $answer->point;
                    if (isset($res['image'])) {
                        // $image_parts = explode(";base64,", $res['image']);
                        // if ($image_parts) {
                        //     $image_type_aux = explode("image/", $image_parts[0]);
                        //     $image_type = $image_type_aux[1];
                        //     if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                        //         $image_base64 = base64_decode($image_parts[1]);
                        //         $folderPath = 'storage/cheklist/';
                        //         $imageName = uniqid();
                        //         $imageFullPath = $folderPath.$imageName.".".$image_type;
                        //         file_put_contents($imageFullPath, $image_base64);
                        //         $res['image'] = $imageFullPath;
                        //     }else{
                        //         return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
                        //     }
                        // }

                        $file = $res['image'];
                        $imageFullPath = $file->store('storage/cheklist');
                        $res['image'] = $imageFullPath;
                    }
                    $res['answer_question_user_id'] = $answerUser->id;
                    $res['answer_question_id'] = $res['answer_id'];
                    unset($res['kendaraan_id']);

                    $CategoryQuestion[$key] = AnswerQuestionDetail::create($res);
                }

                $point = 0;
                foreach ($CategoryQuestion as $answer) {
                    // dd($answer->point);
                    $point += (int)$answer->point;
                }

                // $qPoint['total_point'] = $point/$data['length_category'];
                $statusVehicle = StatusVehicle::where('start', '<=', $point)->where('to', '>=', $point)->first();
                if (!$statusVehicle) {
                    $statusVehicle = StatusVehicle::orderBy('to', 'desc')->first();
                }

                $color = StatusVehicle::where('start', '<=', $point)->where('to', '>=', $point)->first()->id;
                $qPoint['status_vehicle_id'] = $color;
                $qPoint['total_point'] = $point;
                $qPoint['input_date'] = now();
                $qPoint['status'] = 1;
                AnswerQuestionUser::findOrFail($answerUser->id)->update($qPoint);
                // $questionPoint = QuestionMark::findOrFail($idM)->update($qPoint);

                return response()->json(['success' => true, 'message' => 'Selamat anda mendapatkan '.$point.' point']);
            }else {
                return response()->json(['success' => false, 'message' => 'jawaban harus menyertakan gambar', 'question' => $question_accompanied_foto]);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $data = AnswerQuestionDetail::where('answer_question_user_id', $id)->with('answerQuestion', 'answerUser', 'questions.categoryQuestion')->get();
            foreach ($data as $key => $value) {
                $answer_question = AnswerQuestion::where('question_id', $value->question_id)->get();
                $data[$key]['questions']['list_answer_question'] = $answer_question;
            }
            $status = AnswerQuestionUser::with('statusVehicle')->findOrFail($id);

            return response()->json(['data' => $data, 'status' => $status]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th], 500);
        }
    }

    public function approvedQuestion(Request $request)
    {
        try {
            $data = $request->all();
            foreach ($data as $value) {
                $approval = AnswerQuestionDetail::findOrFail($value['question_id']);
                unset($value['question_id']);
                $approval->update($value);
            }
            if ($data[0]['status'] == 1) {
                $message = "Approval Successfully!";
            }elseif ($data[0]['status'] == 0) {
                $message = "Disaproval Successfully!";
            }
            return response()->json(['success' => true, 'message' => $message]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = AnswerQuestionUser::findOrFail($id);
        if ($data['total_point'] <= $data->statusVehicle->start) {
            $data['ready'] = 0;
        }else{
            $data['ready'] = 1;
        }

        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $CategoryQuestion = AnswerQuestionUser::findOrFail($id)->update($data);

            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = AnswerQuestionUser::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }

    public function groupByColor(Request $request)
    {
        try {
            $data = AnswerQuestionUser::where('vehicle_id')->orderBy('id', 'desc')->with('statusVehicle', 'vehicle')->get()->groupBy('status_vehicle_id');
            if ($request->status_vehicle_id) {
                $data = AnswerQuestionUser::where('status_vehicle_id', $request->status_vehicle_id)->where('vehicle_id')->orderBy('id', 'desc')->with('statusVehicle', 'vehicle')->get();
            }

            return response()->json(['success' => true, 'data' => $data]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    public function groupByCategory($id)
    {
        $data = AnswerQuestionDetail::where('answer_question_user_id', $id)->with('questions.categoryQuestion')->get();

        $groupByCategory = array();
        foreach ($data as $key => $value) {
            $groupByCategory[$value->questions->category_question_id][$key]['answer_question_user_id'] = $value->answer_question_user_id;
            $groupByCategory[$value->questions->category_question_id][$key]['point'] = $value->point;
            $groupByCategory[$value->questions->category_question_id][$key]['danger'] = $value->answerQuestion->danger;
            $groupByCategory[$value->questions->category_question_id][$key]['question_id'] = $value->question_id;
            $groupByCategory[$value->questions->category_question_id][$key]['answer_question_id'] = $value->answer_question_id;
            $groupByCategory[$value->questions->category_question_id][$key]['category_question_id'] = $value->questions->category_question_id;
        }
        $point = array();

        foreach ($groupByCategory as $key => $value) {
            $points = 0;
            $danger = false;
            foreach ($value as $cvalue) {
                $points += (int)$cvalue['point'];
                if ($cvalue['danger']) {
                    $danger = true;
                }
            }

            if ($danger == false) {
                $point[$key]['status'] = StatusVehicle::where('start', '<=', $points)->where('to', '>=', $points)->first();
            }else if($danger == true){
                $point[$key]['status'] = array('name' => 'red', 'color' => '#ff0000', 'danger' => true);
            }
            // $point[$key]['status'] = StatusVehicle::where('start', '<=', $points)->where('to', '>=', $points)->first();
            $point[$key]['point'] = $points;
            $point[$key]['question_detail'] = $value;
            $point[$key]['category_question_name'] = CategoryQuestion::findOrFail($key)->name;
            $point[$key]['category_question_id'] = $key;
        }
        return $point;
    }

    public function cheklistStatus($id)
    {
        try {
            $answerQuestionUser = AnswerQuestionUser::findOrFail($id);
            $data['driver'] = Driver::where('vehicle_id', $answerQuestionUser->vehicle_id)->with('user')->first();
            if ($data['driver']) {
                if ($data['driver']->sim_validity_period > now()) {
                    $data['driver']['masa_berlaku'] = 'HIDUP';
                }else if ($data['driver']->sim_validity_period > now()) {
                    $data['driver']['masa_berlaku'] = 'MATI';
                }
            }else{
                $data['driver'] = array('status' => false, 'message' => 'silahkan cek kembali data driver');
            }
            $data['checklist-health'] = AnswerQuestionUser::whereDate('created_at', Carbon::now())->where('user_id', $answerQuestionUser->user_id)->where('slug', 'checklist-health')->orderBy('id', 'desc')->get();
            $data['checklist-vehicle'] = AnswerQuestionUser::whereDate('created_at', Carbon::now())->where('user_id', $answerQuestionUser->user_id)->where('slug', 'checklist-vehicle')->with('vehicle')->orderBy('id', 'desc')->get();
            return response()->json($data);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    // WITH IMAGE BASE64
    public function createQuestionUser(Request $request)
    {
        try {
            $data = $request->all();

            if ($data['qr_code'] && $data['km_actual'] && $data['tanggal']) {
                if ($data['qr_code']) {
                    // $vehicle = Vehicle::where('qr_code', $data['qr_code'])->first();

                    // FILTER KENDARAAN PULANG DGN KENDARAAN SAMA
                    $checklist_terakhir = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','checklist-vehicle')->whereNotNull('status_vehicle_id')->orderBy('id','desc')->first();
                    $request['category'] = 'VEHICLE';
                    $asset = Asset::where('code', $data['qr_code'])->filter($request)->first();

                    if ($asset) {
                        if ($checklist_terakhir && $checklist_terakhir->type == 1) {
                            // if ($checklist_terakhir->vehicle_repair == 1 && $checklist_terakhir->asset_id == $asset->id) {
                            //     return response()->json(['status' => true, 'message' => 'kendaraan tidak bisa digunakan']);
                            // }
                            if ($checklist_terakhir->asset_id != $asset->id && $checklist_terakhir->vehicle_repair == null) {
                                return response()->json(['status' => true, 'message' => 'kendaraan tidak sesuai']);
                            }
                        }

                        if (($asset->km_actual - 1) >= $data['km_actual']) {
                            return response()->json(['success' => false, 'message' => 'km actual tidak sesuai!']);
                        }

                        // if ($asset->maintenance == 1) {
                        //     return response()->json(['status' => true, 'message' => 'kendaraan tidak bisa digunakan']);
                        // }

                        if (isset($asset->trip) && $asset->user_id != Auth::user()->id) {
                            return response()->json(['status' => true, 'message' => 'kendaraan masih digunakan']);
                        }

                        $limit_odometer_kuning = $asset->km_actual + 8000;
                        $limit_odometer_red = $asset->km_actual + 10000;
                        $maintenance_odometer = 0;
                        if ($asset->maintenance_odometer == 0 && $asset->km_actual < $limit_odometer_kuning) {
                            $asset->maintenance_odometer = 1;
                            $maintenance_odometer = 1;
                        }else if ($asset->maintenance_odometer == 0 && $asset->km_actual < $limit_odometer_red) {
                            $asset->maintenance_odometer = 2;
                            $maintenance_odometer = 2;
                        }

                        if ($maintenance_odometer == 0 && $asset->km_actual > 999999) {
                            $asset->km_actual = 0 + $data['km_actual'];
                        }

                        $asset->save();
                        // if (!$vehicle) {
                        //     return response()->json(['success' => false, 'message' => 'kode scan qr tidak terdaftar']);
                        // }
                        if (!$asset) {
                            return response()->json(['success' => false, 'message' => 'kode scan qr tidak terdaftar']);
                        }
                        // $check_nopol = Vehicle::where('qr_code', $data['qr_code'])
                        // ->where('license_no', $data['license_no'])
                        // ->first();
                        // if (!$check_nopol) {
                        //     return response()->json(['success' => false, 'message' => 'no pol tidak sesuai dengan kendaraan yang di scan']);
                        // }
                        $driver = Driver::where('user_id', Auth::user()->id)->update(['asset_id' => $asset->id]);
                        if ($driver) {
                            // $answerQuestionUser['vehicle_id'] = $vehicle->id;
                            $answerQuestionUser['sealed'] = $data['sealed'] ?? null;
                            $answerQuestionUser['sealed_reason'] = $data['sealed_reason'] ?? null;
                            $answerQuestionUser['asset_id'] = $asset->id;
                            $answerQuestionUser['status'] = 0;
                            $answerQuestionUser['input_date'] = now();
                            $answerQuestionUser['user_id'] = Auth::user()->id;
                            $answerQuestionUser['slug'] = 'checklist-vehicle';
                            $answerQuestionUser['maintenance_odometer'] = $maintenance_odometer;
                            if ($request->image) {
                                $image_parts = explode(";base64,", $request->image);
                                if ($image_parts) {
                                    $image_type_aux = explode("image/", $image_parts[0]);
                                    $image_type = $image_type_aux[1];
                                    if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                                        $image_base64 = base64_decode($image_parts[1]);
                                        $folderPath = 'storage/image_odometer/';
                                        $imageName = uniqid();
                                        $imageFullPath = $folderPath.$imageName.".".$image_type;
                                        file_put_contents($imageFullPath, $image_base64);
                                        $answerQuestionUser['image_odometer'] = $imageFullPath;
                                    }else{
                                        return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
                                    }
                                }
                            }
                            if (!isset($data['type'])) {
                                $answerQuestionUser['type'] = 1;
                            }else{
                                if ($data['type'] == 2) {
                                    $get_last_berangkat = AnswerQuestionUser::where('user_id',Auth::user()->id)
                                    ->where('slug','checklist-vehicle')
                                    ->orderBy('id','desc')
                                    ->first();

                                    if ($get_last_berangkat) {
                                        if ($get_last_berangkat->asset_id != $asset->id) {
                                            return response()->json(['success' => false, 'message' => 'Kendaraan tidak sesuai dengan kendaraan berangkat']);
                                        }
                                    }else{
                                        return response()->json(['success' => false, 'message' => 'Anda belum melakukan checklist kendaraan berangkat']);
                                    }
                                }
                                $answerQuestionUser['type'] = $data['type'];
                            }
                            $answerUser = AnswerQuestionUser::create($answerQuestionUser);

                            $dataActual = new VehicleKmActual;
                            // $dataActual->id_vehicle = $vehicle->id;
                            $dataActual->asset_id = $asset->id;
                            $dataActual->km_actual = $data['km_actual'];
                            $dataActual->trip = $data['trip'];
                            $dataActual->tanggal = $data['tanggal'];
                            $dataActual->answer_question_user_id = $answerUser->id;
                            $dataActual->save();

                            $license_no = $asset->assetDetail->where('attribute_code', 'nomor_polisi')->first()->value;

                            return response()->json(['status' => true, 'message' => 'success', 'answer_question_user_id' => $answerUser->id, 'license_no' => $license_no]);
                        }else{
                            return response()->json(['status' => true, 'message' => 'periksa kembali kendaraan anda']);
                        }
                    }else{
                        return response()->json(['success' => false, 'message' => 'kode scan qr tidak terdaftar']);
                    }
                }
            }else{
                return response()->json(['success' => false, 'message' => 'periksa kembali parameter']);
            }
        } catch (\Throwable $th) {
            Log::info($th);
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    public function storeByCategory(Request $request)
    {
        try{
            $data = $request->all();

            $accompanied_foto = false;
            $question_accompanied_foto = array();
            foreach ($data['answer'] as $key => $value) {
                $question = Question::findOrFail($value['question_id']);
                if ($question->accompanied_photo == 1 && !(isset($value['image']))) {
                    $accompanied_foto = true;
                    $question_accompanied_foto[$key] = $value['question_id'];
                }
            }

            $total_poin = 0;
            $point_result = 0;
            $danger = (bool)false;
            $ket_danger = [];
            $message = "";
            if ($accompanied_foto == false) {

                $CategoryQuestion = array();
                foreach ($data['answer'] as $key => $res) {
                    // return $res;
                    $question = Question::findOrFail($res['question_id']);
                    $answer = AnswerQuestion::findOrFail($res['answer_id']);
                    $poin_tertinggi = AnswerQuestion::where('question_id',$res['question_id'])->orderBy('point','desc')->first();
                    if ($poin_tertinggi) {
                        $total_poin += $poin_tertinggi->point;
                    }

                    $res['question_id'] = $question->id;
                    $res['question'] = $question->question;
                    $res['answer'] = $answer->answer;
                    $res['point'] = $answer->point;
                    $res['note_checklist'] = $answer->note_checklist;

                    if ($answer->danger == 1) {
                        $danger = (bool)true;
                        $ket_danger[] = $question->question;
                    }

                    if (isset($res['image'])) {
                        // $image_parts = explode(";base64,", $res['image']);
                        // if ($image_parts) {
                        //     $image_type_aux = explode("image/", $image_parts[0]);
                        //     $image_type = $image_type_aux[1];
                        //     if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                        //         $image_base64 = base64_decode($image_parts[1]);
                        //         $folderPath = 'storage/cheklist/';
                        //         $imageName = uniqid();
                        //         $imageFullPath = $folderPath.$imageName.".".$image_type;
                        //         file_put_contents($imageFullPath, $image_base64);
                        //         $res['image'] = $imageFullPath;
                        //     }else{
                        //         return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
                        //     }
                        // }

                        $file = $res['image'];
                        $imageFullPath = $file->store('storage/cheklist');
                        $res['image'] = $imageFullPath;
                    }

                    $res['answer_question_user_id'] = $data['answer_question_user_id'];
                    $res['answer_question_id'] = $res['answer_id'];

                    AnswerQuestionDetail::create($res);
                    $point_result += $answer->point;
                }

                $point = 0;
                $danger = false;
                foreach ($CategoryQuestion as $answer) {
                    $point += (int)$answer->point;
                    if ($answer->danger) {
                        // $danger = true;
                    }
                }

                if ($point_result > 0) {
                    $total_persen = ($point_result/$total_poin)*100;
                }else{
                    $total_persen = 0;
                }

                if ($danger) {
                    $color = StatusVehicle::where('name','red')->first();
                    $status_color = 'red';
                    $message = 'tidak lulus check karena kriteria berikut bermasalah:\n';
                    foreach ($ket_danger as $key_danger => $val_danger) {
                        $message .= '- '.$val_danger.'\n';
                    }
                }else{
                     if($total_persen < 100){
                        $color = StatusVehicle::where('name','red')->first();
                        $status_color = 'red';
                        $message = 'tidak lulus check karena salah satu kriteria bermasalah';
                    }else{
                        $color = StatusVehicle::where('name','green')->first()->id;
                        $status_color = 'green';
                        $message = 'lulus check silahkan lanjut check kendaraan';
                    }
                    // $color = StatusVehicle::where('name','green')->first()->id;
                    // $status_color = 'green';
                    // $message = 'lulus check silahkan lanjut check kendaraan';
                }

                return response()->json(['success' => true, 'status' => $status_color, 'poin' => round($total_persen,0), 'message' => $message, 'question' => '']);
            }else {
                return response()->json(['success' => false, 'status' => '', 'poin' => 0, 'message' => $message, 'question' => $question_accompanied_foto]);
            }

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'status' => '', 'poin' => 0, 'message' => $e->getMessage(), 'question' => $question_accompanied_foto], 500);
        }
    }

    public function finishStoreByCategory(Request $request)
    {
        try{
            $data = $request->all();
            Log::info($data);

            $total_poin = 0;
            $danger = (bool)false;
            $ket_danger = [];
            $answer_all = AnswerQuestionDetail::where('answer_question_user_id', $data['answer_question_user_id'])->get();
            foreach ($answer_all as $key => $datas) {
                $poin_tertinggi = AnswerQuestion::where('question_id',$datas->question_id)->orderBy('point','desc')->first();
                if ($poin_tertinggi) {
                    $total_poin += $poin_tertinggi->point;
                }

                if ($datas->danger == 1) {
                    $danger = (bool)true;
                    $ket_danger[] = $datas->question->question;
                }
            }

            $point = AnswerQuestionDetail::where('answer_question_user_id', $data['answer_question_user_id'])->sum('point');

            if ($point > 0) {
                $total_persen = ($point/$total_poin)*100;
            }else{
                $total_persen = 0;
            }

            $qPoint['total_point'] = $total_persen;

            $message = "";
            if ($danger) {
                $color = StatusVehicle::where('name','red')->first()->id;
                $status_color = 'red';
                $message = 'tidak lulus check karena kriteria berikut bermasalah:\n';
                foreach ($ket_danger as $key_danger => $val_danger) {
                    $message .= '- '.$val_danger.'\n';
                }
            }elseif ($total_persen == 100) {
                $color = StatusVehicle::where('name','green')->first()->id;
                $status_color = 'green';
                $message = 'Anda lulus check kendaraan silahkan lanjut';
            }elseif ($total_persen < 97) {
                $color = StatusVehicle::where('name','red')->first()->id;
                $status_color = 'red';
                $message = 'tidak lulus check karena hasil nilai kendaraan anda rendah';
            }elseif ($total_persen >= 97 && $total_persen < 99) {
                $color = StatusVehicle::where('name','yellow')->first()->id;
                $status_color = 'yellow';
                $message = 'Anda lulus check kendaraan silahkan lanjut';
            }else{
                $color = StatusVehicle::where('name','green')->first()->id;
                $status_color = 'green';
                $message = 'Anda lulus check kendaraan silahkan lanjut';
            }
            $qPoint['status_vehicle_id'] = $color;
            $qPoint['status'] = 1;
            $dataAqu = AnswerQuestionUser::findOrFail($data['answer_question_user_id'])->update($qPoint);

            $aqu = AnswerQuestionUser::findOrFail($data['answer_question_user_id']);

            $last_trip = VehicleKmActual::where('asset_id', $aqu->asset_id)->where('answer_question_user_id', $aqu->id)->first() ?? null;
            $asset = Asset::findOrFail($aqu->asset_id);
            $asset->km_actual = VehicleKmActual::where('answer_question_user_id', $aqu->id)->first()->km_actual;
            if (isset($last_trip)) {
                if ($aqu->type == 1) {
                    $asset->user_id = $aqu->user_id;
                    if ($last_trip->trip == 1) {
                        $asset->trip = 1;
                    }else{
                        $asset->trip = 0;
                    }
                }else if($aqu->type == 2){
                    $asset->trip = null;
                    $asset->user_id = null;
                }
            }
            $asset->save();

            return response()->json(['success' => true, 'status' => $status_color, 'poin' => round($total_persen,0), 'message' => $message, 'question' => '']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'status' => '', 'poin' => 0, 'message' => $e->getMessage(), 'question' => ''], 500);
        }
    }

    public function inputNopol(Request $request)
    {
        $data = $request->all();
        $sim = Auth::user()->driver->type_sim->name;
        $vhcl = Vehicle::where('license_no', $data['license_no'])->first();

        if ($sim == 'B2 UMUM') {
            return response()->json(['status' => true, 'message' => 'sim memenuhi kriteria', 'data' => $vhcl]);
        }elseif ($sim == 'B1 UMUM' && $vhcl->minimum_sim != 'B2 UMUM') {
            return response()->json(['status' => true, 'message' => 'sim memenuhi kriteria', 'data' => $vhcl]);
        }elseif ($sim == 'B1' && $vhcl->minimum_sim != 'B1 UMUM' && $vhcl->minimum_sim != 'B2 UMUM') {
            return response()->json(['status' => true, 'message' => 'sim memenuhi kriteria', 'data' => $vhcl]);
        }elseif ($sim == 'A' && $vhcl->minimum_sim == 'A') {
            return response()->json(['status' => true, 'message' => 'sim memenuhi kriteria', 'data' => $vhcl]);
        }elseif ($sim == 'C' && $vhcl->minimum_sim == 'C') {
            return response()->json(['status' => true, 'message' => 'sim memenuhi kriteria', 'data' => $vhcl]);
        }else{
            return response()->json(['status' => false, 'message' => 'hubungin kordi anda karena sim yg anda miliki tidak sesuai dengan jenis kendaraan yang anda bawa']);
        }
    }

    public function detailCheck($id){
        $data = AnswerQuestionUser::find($id);
        if (!$data) {
            return response()->json(['success' => false, 'message' => 'terjadi kesalahan']);
        }

        $output['waktu_checklist'] = $data->input_date;
        $output['waiting'] = $data->waiting;

        $data['riwayat'] = RiwayatAnswerQuestionUser::where('answer_question_user_id', $data->id)->orderBy('id', 'desc')->first();

        if ($data['riwayat']) {
            $output['approveBy'] = $data['riwayat']->approveBy;
            $output['approveBy']['fullname'] = $data['riwayat']->approveBy->first_name." ".$data['riwayat']->approveBy->last_name;
        }

        if (isset($data['riwayat']['status_k3'])) {
            $output['decision_status'] = Helper::status_k3($data['riwayat']['status_k3']);
        }else if (isset($data['riwayat']['status_koordinator'])) {
            $output['decision_status'] = Helper::status_koordinator($data['riwayat']['status_koordinator']);
        }else{
            $output['decision_status'] = null;
        }

        $output['kode_qr_kendaraan'] = $data->asset->code ?? null;
        $output['nopol_kendaraan'] = AssetDetail::where('asset_id', $data->asset_id)->where('attribute_code', 'nomor_polisi')->first()->value ?? null;
        if (isset($data->statusVehicle->name)) {
            $output['status'] = $data->statusVehicle->name;
        }else{
            $output['status'] = null;
        }
        $output['note'] = $data['riwayat']->note ?? null;
        $output['poin'] = $data->total_point ?? null;

        return response()->json(['success' => true, 'data' => $output]);
    }

    public function masukBengkel($id)
    {
        try {
            $data['vehicle_repair'] = true;
            $update = AnswerQuestionUser::findOrFail($id)->update($data);

            $aqu = AnswerQuestionUser::findOrFail($id);

            $datas['answer_question_user_id'] = $id;
            $datas['status_koordinator'] = 0;
            $datas['user_id'] = Auth::user()->id;
            $datas['note'] = 'Masuk Bengkel';
            $raqu = RiwayatAnswerQuestionUser::create($datas);

            $dataWorkshop = new Workshop();
            // $dataWorkshop->no_seri = 'LJR-'.now()->format('YmdHis').rand(10, 99);
            $dataAsset = Asset::find($aqu->asset_id);
            $company_id = $dataAsset->company_id;
            $department_id = $dataAsset->department_id;
            $location_id = 00;
            $dataWorkshop->no_seri = Helper::genCode($company_id, $department_id, $location_id,01, 'SV') ?? 'SV';
            $dataWorkshop->asset_id = $aqu->asset_id;
            // $dataWorkshop->type = 'Perbaikan';
            $dataWorkshop->type = 'Service';
            $dataWorkshop->user_id = Auth::user()->id;
            $dataWorkshop->driver_id = $aqu->user_id;
            $dataWorkshop->status_perbaikan_id = 1;
            $dataWorkshop->answer_question_user_id = $aqu->id;
            $dataWorkshop->km_service = Asset::findOrFail($aqu->asset_id)->km_actual;
            $dataWorkshop->save();

            $asset = Asset::findOrFail($aqu->asset_id);
            // $asset->maintenance = 1; hide feature maintenance
            $asset->save();

            return response()->json(['success' => true, 'message' => 'Update data berhasil']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function tetapLanjut($id)
    {
        try {
            $data['vehicle_next_danger'] = true;
            $aqu = AnswerQuestionUser::findOrFail($id)->update($data);

            $datas['answer_question_user_id'] = $id;
            $datas['status_koordinator'] = 1;
            $datas['user_id'] = Auth::user()->id;
            $datas['note'] = 'Tetap Lanjut';
            $raqu = RiwayatAnswerQuestionUser::create($datas);

            return response()->json(['success' => true, 'message' => 'Update data berhasil']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function emergency(Request $request)
    {
        $data = $request->all();

        $driver = Driver::where('user_id', Auth::user()->id)->first()->vehicle ?? null;

        if ($driver) {
            $driver->update($data);
            return response()->json(['success' => true, 'message' => 'Update data berhasil']);
        }else {
            return response()->json(['success' => true, 'message' => 'data kendaraan tidak ditemukan']);
        }
    }

    public function createQuestionVehicleUser(Request $request)
    {
        try {
            $data = $request->all();
            if ($data['qr_code'] && $data['km_actual'] ?? 0 && $data['tanggal']) {
                if ($data['qr_code']) {
                    // $vehicle = Vehicle::where('qr_code', $data['qr_code'])->first();

                    // FILTER KENDARAAN PULANG DGN KENDARAAN SAMA
                    $checklist_terakhir = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','checklist-vehicle')->whereNotNull('status_vehicle_id')->orderBy('id','desc')->first();
                    $request['category_asset_name'] = 'VEHICLE';
                    $asset = Asset::where('code', trim($data['qr_code']))->filter($request)->first();

                    if (!$asset) {
                        return response()->json(['success' => false, 'message' => 'Kode scan qr tidak terdaftar, Periksa kembali kode qr anda!']);
                    }

                    if ($checklist_terakhir && $checklist_terakhir->type == 1) {
                        // if ($checklist_terakhir->vehicle_repair == 1 && $checklist_terakhir->asset_id == $asset->id) {
                        //     return response()->json(['status' => true, 'message' => 'kendaraan tidak bisa digunakan']);
                        // }
                        if ($checklist_terakhir->asset_id != $asset->id && $checklist_terakhir->vehicle_repair == null) {
                            return response()->json(['status' => true, 'message' => 'kendaraan tidak sesuai']);
                        }
                    }

                    if (($asset->km_actual - 1) >= $data['km_actual']) {
                        return response()->json(['success' => false, 'message' => 'km actual tidak sesuai!']);
                    }

                    // if ($asset->maintenance == 1) {
                    //     return response()->json(['status' => true, 'message' => 'Kendaraan tidak bisa digunakan karena kendaraan belum Maintenance']);
                    // }

                    if (isset($asset->trip) && $asset->user_id != Auth::user()->id) {
                        return response()->json(['status' => true, 'message' => 'kendaraan masih digunakan']);
                    }

                    // $limit_odometer_kuning = $asset->km_actual + 8000;
                    // $limit_odometer_red = $asset->km_actual + 10000;
                    // $maintenance_odometer = 0;
                    // if ($asset->maintenance_odometer == 0 && $asset->km_actual < $limit_odometer_kuning) {
                    //     $asset->maintenance_odometer = 1;
                    //     $maintenance_odometer = 1;
                    // }else if ($asset->maintenance_odometer == 0 && $asset->km_actual < $limit_odometer_red) {
                    //     $asset->maintenance_odometer = 2;
                    //     $maintenance_odometer = 2;
                    // }

                    // if ($maintenance_odometer == 0 && $asset->km_actual > 999999) {
                    //     $asset->km_actual = 0 + $data['km_actual'];
                    // }
                    $limit_odometer_kuning = 8000;
                    $limit_odometer_red = 10000;

                    $maintenance_odometer = 0;
                    if ($asset->maintenance_odometer == 0 && $data['km_actual'] < $limit_odometer_kuning) {
                        $asset->maintenance_odometer = 0;
                        $maintenance_odometer = 0;
                    }else if ($asset->maintenance_odometer == 1 && $data['km_actual'] >= $limit_odometer_kuning && $data['km_actual'] < $limit_odometer_red) {
                        $asset->maintenance_odometer = 1;
                        $maintenance_odometer = 1;
                    }else if ($asset->maintenance_odometer == 1 || $asset->maintenance_odometer == 0 && $data['km_actual'] >= $limit_odometer_red) {
                        $asset->maintenance_odometer = 2;
                        $maintenance_odometer = 2;
                    }

                    if($checklist_terakhir && $checklist_terakhir->type == 1)
                    {
                        $asset->km_actual = $data['km_actual'];
                    }else{
                        $asset->km_actual = $asset->km_actual;
                    }

                    $asset->save();

                    $driver = Driver::where('user_id', Auth::user()->id)->update(['asset_id' => $asset->id]);
                    if ($driver) {
                        // $answerQuestionUser['vehicle_id'] = $vehicle->id;
                        $answerQuestionUser['sealed'] = $data['sealed'] ?? null;
                        $answerQuestionUser['sealed_reason'] = $data['sealed_reason'] ?? null;
                        $answerQuestionUser['asset_id'] = $asset->id;
                        $answerQuestionUser['status'] = 0;
                        $answerQuestionUser['input_date'] = now();
                        $answerQuestionUser['user_id'] = Auth::user()->id;
                        $answerQuestionUser['slug'] = 'checklist-vehicle';
                        $answerQuestionUser['maintenance_odometer'] = $maintenance_odometer;
                        if ($request->hasFile('image')) {
                            $image = $request->file('image');
                            $path = $image->store('storage/image_odometer');
                            $answerQuestionUser['image_odometer'] = $path;
                        }
                        if (!isset($data['type'])) {
                            $answerQuestionUser['type'] = 1;
                        }else{
                            if ($data['type'] == 2) {
                                $get_last_berangkat = AnswerQuestionUser::where('user_id',Auth::user()->id)
                                ->where('slug','checklist-vehicle')
                                ->orderBy('id','desc')
                                ->first();

                                if ($get_last_berangkat) {
                                    if ($get_last_berangkat->asset_id != $asset->id) {
                                        return response()->json(['success' => false, 'message' => 'Kendaraan tidak sesuai dengan kendaraan berangkat']);
                                    }
                                }else{
                                    return response()->json(['success' => false, 'message' => 'Anda belum melakukan checklist kendaraan berangkat']);
                                }
                            }
                            $answerQuestionUser['type'] = $data['type'];
                        }
                        $answerUser = AnswerQuestionUser::create($answerQuestionUser);

                        $dataActual = new VehicleKmActual;
                        // $dataActual->id_vehicle = $vehicle->id;
                        $dataActual->asset_id = $asset->id;
                        $dataActual->km_actual = $data['km_actual'];
                        $dataActual->trip = $data['trip'];
                        $dataActual->tanggal = $data['tanggal'];
                        $dataActual->answer_question_user_id = $answerUser->id;
                        $dataActual->save();

                        $license_no = $asset->assetDetail->where('attribute_code', 'nomor_polisi')->first()->value;

                        if($checklist_terakhir && $checklist_terakhir->type == 1)
                        {
                            return response()->json(['status' => true, 'answer_question_user_id' => $answerUser->id, 'type'=> 0, 'license_no' => $license_no, 'message' => 'success',]);
                        }else{
                            if ($asset->maintenance_odometer == 1) {
                                return response()->json(['status' => true, 'answer_question_user_id' => $answerUser->id,'type'=> 1, 'license_no' => $license_no, 'message' => 'Km Odometer melebihi ketentuan dijadwalkan masuk bengkel']);
                            }else if ($asset->maintenance_odometer == 2) {
                                return response()->json(['status' => false, 'answer_question_user_id' => $answerUser->id,'type'=> 2, 'license_no' => $license_no, 'message' => 'Km Odometer melebihi ketentuan harus masuk bengkel terlebih dahulu']);
                            }else{
                                return response()->json(['status' => true, 'answer_question_user_id' => $answerUser->id, 'type'=> 0,'license_no' => $license_no, 'message' => 'success' ]);
                            }
                        }
                    }else{
                        return response()->json(['status' => true, 'message' => 'periksa kembali kendaraan anda']);
                    }
                }
            }else{
                return response()->json(['success' => false, 'message' => 'periksa kembali parameter']);
            }
        } catch (\Throwable $th) {
            Log::info($th);
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    // OLD this->storeByCategory
    public function storeByCategoryVehicle(Request $request)
    {
        try{
            $data = $request->all();

            $accompanied_foto = false;
            $question_accompanied_foto = array();
            foreach ($data['answer'] as $key => $value) {
                $question = Question::findOrFail($value['question_id']);
                if ($question->accompanied_photo == 1 && !(isset($value['image']))) {
                    $accompanied_foto = true;
                    $question_accompanied_foto[$key] = $value['question_id'];
                }
            }

            $total_poin = 0;
            $point_result = 0;
            $danger = (bool)false;
            $ket_danger = [];
            $message = "";
            if ($accompanied_foto == false) {

                $CategoryQuestion = array();
                foreach ($data['answer'] as $key => $res) {
                    // return $res;
                    $question = Question::findOrFail($res['question_id']);
                    $answer = AnswerQuestion::findOrFail($res['answer_id']);
                    $poin_tertinggi = AnswerQuestion::where('question_id',$res['question_id'])->orderBy('point','desc')->first();
                    if ($poin_tertinggi) {
                        $total_poin += $poin_tertinggi->point;
                    }

                    $res['question_id'] = $question->id;
                    $res['question'] = $question->question;
                    $res['answer'] = $answer->answer;
                    $res['point'] = $answer->point;
                    $res['note_checklist'] = $answer->note_checklist;

                    if ($answer->danger == 1) {
                        $danger = (bool)true;
                        $ket_danger[] = $question->question;
                    }

                    // FILE
                    // if (isset($res['image'])) {
                    //     $file = $res['image'];
                    //     $file_path = $file->store('storage/cheklist');
                    //     $res['image'] = $file_path;
                    // }

                    $res['image'] = $res['image'] ?? null;

                    $res['answer_question_user_id'] = $data['answer_question_user_id'];
                    $res['answer_question_id'] = $res['answer_id'];

                    AnswerQuestionDetail::create($res);
                    $point_result += $answer->point;
                }

                $point = 0;
                $danger = false;
                foreach ($CategoryQuestion as $answer) {
                    $point += (int)$answer->point;
                    if ($answer->danger) {
                        // $danger = true;
                    }
                }

                if ($point_result > 0) {
                    $total_persen = ($point_result/$total_poin)*100;
                }else{
                    $total_persen = 0;
                }

                if ($danger) {
                    $color = StatusVehicle::where('name','red')->first();
                    $status_color = 'red';
                    $message = 'tidak lulus check karena kriteria berikut bermasalah:\n';
                    foreach ($ket_danger as $key_danger => $val_danger) {
                        $message .= '- '.$val_danger.'\n';
                    }
                }else{
                    if($total_persen < 100){
                        $color = StatusVehicle::where('name','red')->first();
                        $status_color = 'red';
                        $message = 'tidak lulus check karena salah satu kriteria bermasalah';
                    }else{
                        $color = StatusVehicle::where('name','green')->first()->id;
                        $status_color = 'green';
                        $message = 'lulus check silahkan lanjut check kendaraan';
                    }
                }

                return response()->json(['success' => true, 'status' => $status_color, 'poin' => round($total_persen,0), 'message' => $message, 'question' => '']);
            }else {
                return response()->json(['success' => false, 'status' => '', 'poin' => 0, 'message' => $message, 'question' => $question_accompanied_foto]);
            }

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'status' => '', 'poin' => 0, 'message' => $e->getMessage(), 'question' => $question_accompanied_foto], 500);
        }
    }

    public function uploadImageChecklist(Request $request)
    {
        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $file_path = $file->store('storage/cheklist');
            $base_url_path =  url('/').'/'.$file_path;
            return response()->json(['success' => true, 'message' => 'Upload File Successfully', 'file_path' => $file_path, 'base_url_path' => $base_url_path]);
        }else{
            return response()->json(['success' => false, 'message' => 'file tidak ditemukan']);
        }
    }

    public function searchHistoryChecklist(Request $request)
    {
        $data = Asset::with(['user:id,full_name,username,email','AnswerQuestionUser' => function($query) {
                        $query->whereNotNull('status_vehicle_id')
                                ->latest();
                    },'assetDetail' => function($query) {
                        $query->where('attribute_code', 'nomor_polisi');
                    }, 'AnswerQuestionUser.riwayatTindakan.approveBy', 'AnswerQuestionUser.statusVehicle'])
                    ->whereHas('assetDetail', function($query) use ($request) {
                        $query->where('value', 'ilike', '%'.$request->nopolChecklist.'%');
                    })
                    ->orderBy('id', 'desc')->first();

        if($data) {
            $data->license_no = $data->assetDetail->first()->value ?? '';
            $data->driver_name = $data->user->full_name ?? '';
        }

        return response()->json(['success' => true, 'data' => $data]);
    }

    public function resetHistoryChecklist(Request $request, $id)
    {
        try {
            $asset = Asset::find($id);
            if($asset->user_id) {
                $dataAnswareQuestion = new AnswerQuestionUser;
                $dataAnswareQuestion->status = 0;
                $dataAnswareQuestion->user_id = $asset->user_id;
                $dataAnswareQuestion->slug = 'checklist-vehicle';
                $dataAnswareQuestion->asset_id = $id;
                $dataAnswareQuestion->status_vehicle_id = 2;
                $dataAnswareQuestion->type = 2;
                $dataAnswareQuestion->input_date = date('Y-m-d H:i:s');
                $dataAnswareQuestion->save();
            }
            $asset->update(['trip' => null, 'user_id' => null, 'km_actual' => null]);

            return response()->json(['success' => true, 'message' => 'Berhasil reset history checklist']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function scanVehicleSecurity(Request $request)
    {
        $request->validate(['qr_code' => 'required']);

        $asset = Asset::with([
            'user:id,full_name,username,email',
            'assetDetail' => fn($q) => $q->where('attribute_code', 'nomor_polisi')
        ])->where('code', $request->qr_code)
          ->latest()
          ->first();

        if (!$asset) {
            return response()->json(['status' => false, 'message' => 'Kode qr tidak terdaftar']);
        }

        $data = [
            'driver_name' => $asset->user->full_name ?? null,
            'scan_by_name' => auth()->user()->full_name ?? null,
            'scan_by_id' => auth()->user()->id ?? null,
            'police_number' => $asset->assetDetail->first()->value ?? null,
            'kode_asset' => $asset->code ?? null,
            'scan_timestamp' => now()->format('Y-m-d H:i:s')
        ];

        $baseQuery = AnswerQuestionUser::with(['riwayatTindakan.approveBy', 'statusVehicle'])
            ->whereNotNull(['status_vehicle_id', 'total_point', 'status'])
            ->where('status', 1)
            ->latest();

        //get checlist health
        $data['checklist_health'] = (clone $baseQuery)
            ->whereIn('slug', ['checklist-health', 'quiz'])
            ->where('user_id', $asset->user_id)
            ->first() ?? null;
        //get checlist vehicle berangkat
        $data['checklist_vehicle_berangkat'] = (clone $baseQuery)
            ->where([
                'type' => 1, 
                'asset_id' => $asset->id,
                'user_id' => $asset->user_id
            ])
            ->whereIn('slug', ['checklist-vehicle', 'quiz'])
            ->first() ?? null;
        //get checlist vehicle pulang
        $data['checklist_vehicle_pulang'] = (clone $baseQuery)
            ->where([
                'type' => 2, 
                'asset_id' => $asset->id,
                'user_id' => $asset->user_id
            ])
            ->whereIn('slug', ['checklist-vehicle', 'quiz'])
            ->first() ?? null;

        //store logs for history security
        try {
            AssetSecurityLog::create([
                'driver_name' => $data['driver_name'],
                'user_id' => $data['scan_by_id'],
                'user_name' => $data['scan_by_name'],
                'scan_timestamp' => $data['scan_timestamp'],
                'asset_id' => $asset->id,
                'police_number' => $data['police_number'],
                'log_description' => [
                    'scan_info' => [
                        'scan_by_name' => $data['scan_by_name'],
                        'scan_by_id' => $data['scan_by_id'],
                        'scan_timestamp' => $data['scan_timestamp'],
                        'police_number' => $data['police_number'],
                        'kode_asset' => $data['kode_asset']
                    ],
                    'checklist_health' => $data['checklist_health'] ?? null,
                    'checklist_vehicle_berangkat' => $data['checklist_vehicle_berangkat'] ?? null,
                    'checklist_vehicle_pulang' => $data['checklist_vehicle_pulang'] ?? null
                ]
            ]);
        } catch (\Throwable $th) {
            Log::error($th->getMessage());
        }

        return response()->json(['status' => true, 'message' => 'Kode QR Kendaraan Terdaftar', 'data' => $data]);
    }

    public function searchHistoryChecklistSecurity(Request $request)
    {
        $data = AssetSecurityLog::with(['user:id,full_name,username,email', 'asset.user:id,full_name','asset.assetDetail' => function($query) {
                    $query->where('attribute_code', 'nomor_polisi');
                }])
                ->select('*', DB::raw('CAST(log_description AS JSON) as log_description'));

        if($request->keyword) {
            $data->where('police_number', 'like', '%' . $request->keyword . '%');
        }

        if($request->company_id) {
            $data->whereHas('asset', function($query) use ($request) {
                $query->whereIn('company_id', $request->company_id);
            });
        }

        if($request->department_id) {
            $data->whereHas('asset', function($query) use ($request) {
                $query->whereIn('department_id', $request->department_id);
            });
        }

        if($request->location_id) {
            $data->whereHas('asset', function($query) use ($request) {
                $query->whereIn('location_id', $request->location_id);
            });
        }

        if($request->start_date && $request->end_date) {
            $data->whereBetween('scan_timestamp', [$request->start_date, $request->end_date]);
        }
        
        $data = $data->orderBy('id', 'desc')
                    ->paginate($request->per_page ?? 10);
                    
        return response()->json(['success' => true, 'data' => $data]);
    }
}
