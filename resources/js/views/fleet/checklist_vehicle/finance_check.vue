<template>
  <div>
    <div class="card shadow mb-4">
      <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">
          Finance Check
        </h6>
      </div>
      <div class="card-body pb-5">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">
                Cari nomor polisi
              </label>
              <ValidationObserver v-slot="{ handleSubmit }">
                <form @submit.prevent="handleSubmit(formSearchNopol)">
                  <ValidationProvider
                    name="nopol"
                    rules="required|min:3"
                    v-slot="{ errors }"
                  >
                    <div class="d-flex" style="gap: 5px;">
                      <input
                        type="text"
                        class="form-control"
                        name="admin"
                        placeholder="Masukkan nomor polisi"
                        v-model="dataSearch.nopol"
                      />
                      <button class="btn btn-primary">
                        <i class="fas fa-search"></i>
                      </button>
                    </div>
                    <span class="text-error mt-2">{{ errors[0] }}</span>
                  </ValidationProvider>
                </form>
              </ValidationObserver>
            </div>
          </div>
        </div>
        <!-- Finance Check Results -->
        <div class="mt-4" v-if="financeResult">
          <div class="row">
            <div class="col-md-12">
              <div class="card shadow">
                <div class="card-header py-3">
                  <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-check-circle"></i> Hasil Checklist
                  </h6>
                </div>
                <div class="card-body">
                  <!-- Vehicle Information -->
                  <div class="row mb-4">
                    <div class="col-md-12">
                      <div class="card border-left-primary">
                        <div class="card-body p-3">
                          <h6 class="font-weight-bold text-primary mb-3">Informasi Kendaraan</h6>
                          <div class="row">
                            <div class="col-md-6">
                              <div class="mb-2">
                                <small class="text-muted">Nomor Polisi:</small>
                                <div class="font-weight-bold">{{ financeResult.police_number || '-' }}</div>
                              </div>
                              <div class="mb-2">
                                <small class="text-muted">Kode Asset:</small>
                                <div class="font-weight-bold">{{ financeResult.kode_asset || '-' }}</div>
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="mb-2">
                                <small class="text-muted">Driver:</small>
                                <div class="font-weight-bold">{{ financeResult.driver_name || '-' }}</div>
                              </div>
                              <div class="mb-2">
                                <small class="text-muted">Waktu Cek:</small>
                                <div class="font-weight-bold">{{ formatDate(financeResult.scan_timestamp) || '-' }}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- Checklist Status -->
                  <div class="row">
                    <div class="col-md-4 mb-3">
                      <div :class="'card h-100 ' + (financeResult.checklist_health ? getChecklistBorderClass(financeResult.checklist_health) : 'border-left-secondary')">
                        <div class="card-body p-3">
                          <h6 :class="'font-weight-bold mb-2 ' + (financeResult.checklist_health ? getChecklistTextColor(financeResult.checklist_health) : 'text-secondary')">
                            <i class="fas fa-heart"></i> Checklist Kesehatan
                          </h6>
                          <div v-if="financeResult.checklist_health">
                            <div class="mb-2">
                              <small class="text-muted">Status:</small>
                              <span :class="'badge ml-1 ' + getChecklistStatus(financeResult.checklist_health).class">
                                {{ getChecklistStatus(financeResult.checklist_health).text }}
                              </span>
                            </div>
                            <div class="mb-2">
                              <small class="text-muted">Tanggal:</small>
                              <div class="small">{{ formatDate(financeResult.checklist_health.input_date) }}</div>
                            </div>
                            <div class="mb-2">
                              <small class="text-muted">Catatan:</small>
                              <div class="small text-wrap" style="background-color: #f8f9fa; padding: 8px; border-radius: 4px; ">
                                {{ financeResult.checklist_health.note_checklist || "-" }}
                              </div>
                            </div>
                          </div>
                          <div v-else>
                            <small class="text-muted">Status:</small>
                            <span class="badge badge-secondary">Belum Checklist Kesehatan</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="col-md-4 mb-3">
                      <div :class="'card h-100 ' + (financeResult.checklist_vehicle_berangkat ? getChecklistBorderClass(financeResult.checklist_vehicle_berangkat) : 'border-left-secondary')">
                        <div class="card-body p-3">
                          <h6 :class="'font-weight-bold mb-2 ' + (financeResult.checklist_vehicle_berangkat ? getChecklistTextColor(financeResult.checklist_vehicle_berangkat) : 'text-secondary')">
                            <i class="fas fa-car"></i> Checklist Kendaraan (Berangkat)
                          </h6>
                          <div v-if="financeResult.checklist_vehicle_berangkat">
                            <div class="mb-2">
                              <small class="text-muted">Status:</small>
                              <span :class="'badge ml-1 ' + getChecklistStatus(financeResult.checklist_vehicle_berangkat).class">
                                {{ getChecklistStatus(financeResult.checklist_vehicle_berangkat).text }}
                              </span>
                            </div>
                            <div class="mb-2">
                              <small class="text-muted">Tanggal:</small>
                              <div class="small">{{ formatDate(financeResult.checklist_vehicle_berangkat.input_date) }}</div>
                            </div>
                            <div class="mb-2">
                              <small class="text-muted">Catatan:</small>
                              <div class="small text-wrap" style="background-color: #f8f9fa; padding: 8px; border-radius: 4px; ">
                                {{ financeResult.checklist_vehicle_berangkat.note_checklist || "-"}}
                              </div>
                            </div>
                          </div>
                          <div v-else>
                            <small class="text-muted">Status:</small>
                            <span class="badge badge-secondary">Belum Checklist Kendaraan (Berangkat)</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="col-md-4 mb-3">
                      <div :class="'card h-100 ' + (financeResult.checklist_vehicle_pulang ? getChecklistBorderClass(financeResult.checklist_vehicle_pulang) : 'border-left-secondary')">
                        <div class="card-body p-3">
                          <h6 :class="'font-weight-bold mb-2 ' + (financeResult.checklist_vehicle_pulang ? getChecklistTextColor(financeResult.checklist_vehicle_pulang) : 'text-secondary')">
                            <i class="fas fa-car"></i> Checklist Kendaraan (Pulang)
                          </h6>
                          <div v-if="financeResult.checklist_vehicle_pulang">
                            <div class="mb-2">
                              <small class="text-muted">Status:</small>
                              <span :class="'badge ml-1 ' + getChecklistStatus(financeResult.checklist_vehicle_pulang).class">
                                {{ getChecklistStatus(financeResult.checklist_vehicle_pulang).text }}
                              </span>
                            </div>
                            <div class="mb-2">
                              <small class="text-muted">Tanggal:</small>
                              <div class="small">{{ formatDate(financeResult.checklist_vehicle_pulang.input_date) }}</div>
                            </div>
                            <div class="mb-2">
                              <small class="text-muted">Catatan:</small>
                              <div class="small text-wrap" style="background-color: #f8f9fa; padding: 8px; border-radius: 4px; ">
                                {{ financeResult.checklist_vehicle_pulang.note_checklist || "-"}}
                              </div>
                            </div>
                          </div>
                          <div v-else>
                            <small class="text-muted">Status:</small>
                            <span class="badge badge-secondary">Belum Checklist Kendaraan (Pulang)</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Action Button -->
                  <div class="text-center mt-3">
                    <button class="btn btn-secondary" @click="handleResetSearch">
                      <i class="fas fa-search"></i> Reset
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div class="mt-4" v-if="error && !financeResult">
          <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            {{ error }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FlashMessage from "@smartweb/vue-flash-message";
import axios from "axios";
import {
  ValidationProvider,
  ValidationObserver,
} from "vee-validate/dist/vee-validate.full.esm";

Vue.use(FlashMessage);

Vue.config.productionTip = false;

export default {
  name: "Tables",
  mounted() {},
  components: {
    ValidationProvider,
    ValidationObserver,
  },

  data: () => ({
    dataSearch: {
      nopol: "",
    },
    data: {
      id: null,
      answer_question_user: [],
    },
    financeResult: null,
    isLoading: false,
    error: null,
  }),

  created() {},
  methods: {
    getChecklistStatus(checklistData) {
      if (!checklistData || !checklistData.status_vehicle) {
        return {
          text: "Belum di cek",
          class: "badge-secondary"
        };
      }

      const point = checklistData.total_point;
      if (point >= 100) {
        return {
          text: "Normal",
          class: "badge-success"
        };
      } else if (point >= 97 && point <= 99) {
        return {
          text: "Abnormal",
          class: "badge-warning"
        };
      } else {
        return {
          text: "Abnormal",
          class: "badge-danger"
        };
      }
    },

    getChecklistBorderClass(checklistData) {
      if (!checklistData || !checklistData.status_vehicle) {
        return "border-left-secondary";
      }

      const point = checklistData.total_point;
      if (point >= 100) {
        return "border-left-success";
      } else if (point >= 97 && point <= 99) {
        return "border-left-warning";
      } else {
        return "border-left-danger";
      }
    },

    getChecklistTextColor(checklistData) {
      if (!checklistData || !checklistData.status_vehicle) {
        return "text-secondary";
      }

      const point = checklistData.total_point;
      if (point >= 100) {
        return "text-success";
      } else if (point >= 97 && point <= 99) {
        return "text-warning";
      } else {
        return "text-danger";
      }
    },

    formatDate(dateString) {
      if (!dateString) return '-';
      const date = new Date(dateString);
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${day}-${month}-${year} ${hours}:${minutes}`;
    },

    async formSearchNopol() {
      this.isLoading = true;
      this.error = null;
      this.financeResult = null;

      let loader = this.$loading.show({ canCancel: false });

      try {
        const res = await axios.post(
          "checklist-vehicle/scan-vehicle-finance",
          {
            police_number: this.dataSearch.nopol,
          }
        );

        if (res.data?.status) {
          this.financeResult = res.data.data;
          this.$bvToast.toast(res.data.message || "Data berhasil ditemukan", {
            title: "Success",
            variant: "success",
            solid: true,
          });
        } else {
          this.error = res.data?.message || "Data tidak ditemukan";
          this.$bvToast.toast(this.error, {
            title: "Error",
            variant: "danger",
            solid: true,
          });
        }
      } catch (error) {
        this.error = error.response?.data?.message || "Terjadi kesalahan saat mencari data";
        this.$bvToast.toast(this.error, {
          title: "Error",
          variant: "danger",
          solid: true,
        });
      } finally {
        this.isLoading = false;
        loader.hide();
      }
    },
    handleResetSearch() {
      this.data = {
        id: null,
        answer_question_user: [],
      };
      this.dataSearch = {
        nopol: "",
      };
      this.financeResult = null;
      this.error = null;
    },

  },
};
</script>
