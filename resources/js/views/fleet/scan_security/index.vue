<template>
    <div>
        <!-- DataTales Example -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary float-left">Riwayat Scan Security</h6>
                <button @click="showFilterModal" class="btn btn-info btn-sm btn-icon-split float-right">
                    <span class="icon text-white-50">
                        <i class="fas fa-filter"></i>
                    </span>
                    <span class="text">Filter</span>
                </button>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12 mb-3">
                        <v-text-field
                            v-model="search"
                            append-icon="mdi-magnify"
                            label="Search (Asset Code, License Plate)"
                            single-line
                            hide-details
                            @input="debounceSearch"
                        ></v-text-field>
                    </div>
                    <div class="col-12">
                        <vue-good-table
                            :columns="headers"
                            :pagination-options="{
                                enabled: true,
                                perPage: pageLength,
                                server: true,
                            }"
                            :sorting-options="{
                                enabled: true,
                                server: true,
                            }"
                            max-height="500px"
                            :rows="datas"
                            :loading="loading"
                            :empty-state="{
                                enabled: true,
                                text: 'No security scan history found'
                            }"
                            @on-sort-change="(params) => getData(params)"
                            @on-page-change="(params) => getData(params)"
                            >
                            <template
                                slot="table-row"
                                slot-scope="props">
                                <span v-if="props.column.field === 'no'" class="text-nowrap">
                                    <span class="text-nowrap">{{props.row.originalIndex+1}}</span>
                                </span>
                                <span v-else-if="props.column.field === 'scan_by_name'" class="text-nowrap">
                                    <span class="text-nowrap">{{ (props.row.log_description && props.row.log_description.scan_info.scan_by_name) || '-' }}</span>
                                </span>
                                <span v-else-if="props.column.field === 'driver_name'" class="text-nowrap">
                                    <span class="text-nowrap">{{ (props.row.log_description && props.row.asset.user.full_name) || '-' }}</span>
                                </span>
                                <span v-else-if="props.column.field === 'police_number'" class="text-nowrap">
                                    <span class="text-nowrap">{{ props.row.police_number || '-' }}</span>   
                                </span>
                                <span v-else-if="props.column.field === 'kode_asset'" class="text-nowrap">
                                    <span class="text-nowrap">{{ (props.row.log_description && props.row.asset.code) || '-' }}</span>
                                </span>
                                <span v-else-if="props.column.field === 'scan_timestamp'" class="text-nowrap">
                                    <span class="text-nowrap">{{ formatDate(props.row.log_description && props.row.scan_timestamp) }}</span>
                                </span>
                                <span v-else-if="props.column.field === 'created_at'" class="text-nowrap">
                                    <span class="text-nowrap">{{ formatDate(props.row.created_at) }}</span>
                                </span>
                            </template>

                            <template
                                slot="pagination-bottom"
                                slot-scope="props"
                            >
                                <div class="d-flex justify-content-between flex-wrap">
                                    <div class="d-flex align-items-center mb-0 mt-1">
                                        <span class="text-nowrap ">
                                        Showing 1 to
                                        </span>
                                        <b-form-select
                                        v-model="pageLength"
                                        :options="['10','25','50','100']"
                                        class="mx-1"
                                        @input="(value)=>props.perPageChanged({currentPerPage:value})"
                                        />
                                        <span class="text-nowrap"> of {{ totalRows }} entries </span>
                                    </div>
                                    <div>
                                        <b-pagination
                                        :value="1"
                                        :total-rows="totalRows"
                                        :per-page="pageLength"
                                        first-number
                                        last-number
                                        align="right"
                                        prev-class="prev-item"
                                        next-class="next-item"
                                        class="mt-1 mb-0"
                                        @input="(value)=>props.pageChanged({currentPage:value})"
                                        >
                                        <template #prev-text>
                                            <i class="fa fa-arrow-left"></i>
                                        </template>
                                        <template #next-text>
                                            <i class="fa fa-arrow-right"></i>
                                        </template>
                                        </b-pagination>
                                    </div>
                                </div>
                            </template>
                        </vue-good-table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Modal -->
        <b-modal hide-footer size="lg" id="filterModal" title="Filter Riwayat Scan Security">
            <b-row>
                <b-col lg="12" md="12" sm="12" class="mt-3">
                    <form @submit.prevent="applyFilter">
                        <div class="row">
                            <div class="form-group col-md-6">
                                <label for="company">Company</label>
                                <multiselect
                                    v-model="selectedCompanies"
                                    :options="companies"
                                    :multiple="true"
                                    :close-on-select="false"
                                    :clear-on-select="false"
                                    :preserve-search="true"
                                    placeholder="Pilih Company"
                                    label="name"
                                    track-by="id"
                                    class="form-control p-0"
                                    @input="onCompanyChange">
                                    <template slot="selection" slot-scope="{ values, isOpen }">
                                        <span class="multiselect__single" v-if="values.length && !isOpen">
                                            {{ values.length }} company selected
                                        </span>
                                    </template>
                                </multiselect>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="department">Department</label>
                                <multiselect
                                    v-model="selectedDepartments"
                                    :options="departments"
                                    :multiple="true"
                                    :close-on-select="false"
                                    :clear-on-select="false"
                                    :preserve-search="true"
                                    placeholder="Pilih Department"
                                    label="name"
                                    track-by="id"
                                    class="form-control p-0"
                                    @input="onDepartmentChange">
                                    <template slot="selection" slot-scope="{ values, isOpen }">
                                        <span class="multiselect__single" v-if="values.length && !isOpen">
                                            {{ values.length }} department selected
                                        </span>
                                    </template>
                                </multiselect>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="location">Location</label>
                                <multiselect
                                    v-model="selectedLocations"
                                    :options="locations"
                                    :multiple="true"
                                    :close-on-select="false"
                                    :clear-on-select="false"
                                    :preserve-search="true"
                                    placeholder="Pilih Location"
                                    label="name"
                                    track-by="id"
                                    class="form-control p-0"
                                    @input="onLocationChange">
                                    <template slot="selection" slot-scope="{ values, isOpen }">
                                        <span class="multiselect__single" v-if="values.length && !isOpen">
                                            {{ values.length }} location selected
                                        </span>
                                    </template>
                                </multiselect>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="start_date">Start Date</label>
                                <input type="date" class="form-control" v-model="filter.start_date">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="end_date">End Date</label>
                                <input type="date" class="form-control" v-model="filter.end_date">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary mr-2">Apply Filter</button>
                                <button type="button" class="btn btn-secondary" @click="resetFilter">Reset</button>
                            </div>
                        </div>
                    </form>
                </b-col>
            </b-row>
        </b-modal>
    </div>
</template>

<script>
import axios from 'axios'
import { debounce } from 'lodash'
import Multiselect from 'vue-multiselect'

export default {
    name: 'ScanSecurityHistory',

    components: {
        Multiselect
    },
    
    data: () => ({
        headers: [
            {
                label: '#',
                align: 'start',
                sortable: false,
                field: 'no',
            },
            { label: 'Scan By', field: 'scan_by_name' },
            { label: 'Driver Name', field: 'driver_name' },
            { label: 'License Plate', field: 'police_number' },
            { label: 'Asset Code', field: 'kode_asset' },
            { label: 'Scan Time', field: 'scan_timestamp' },
            { label: 'Created At', field: 'created_at' },
        ],
        search: '',
        pageLength: 10,
        datas: [],
        loading: false,
        companies: [],
        departments: [],
        locations: [],
        selectedCompanies: [],
        selectedDepartments: [],
        selectedLocations: [],
        filter: {
            company_id: [],
            department_id: [],
            location_id: [],
            start_date: '',
            end_date: ''
        },
        currentPage: 1,
        currentPerPage: 10,
        sortBy: null,
        sortDesc: false,
        totalRows: 0,
    }),

    created() {
        this.getData({
            currentPage: 1,
            currentPerPage: 10,
            sortBy: null,
            sortDesc: false,
        });
        this.loadFilterOptions();
    },

    methods: {
        getData(params = {}) {
            this.loading = true;

            // Update pagination and sorting state
            this.currentPage = params.currentPage || this.currentPage;
            this.currentPerPage = params.currentPerPage || this.currentPerPage;
            this.sortBy = params.sortBy || this.sortBy;
            this.sortDesc = params.sortDesc || this.sortDesc;

            const requestParams = {
                page: this.currentPage,
                per_page: this.currentPerPage,
                keyword: this.search,
                ...this.filter
            };

            // Only include filter arrays if they have values
            if (this.filter.company_id.length === 0) {
                delete requestParams.company_id;
            }
            if (this.filter.department_id.length === 0) {
                delete requestParams.department_id;
            }
            if (this.filter.location_id.length === 0) {
                delete requestParams.location_id;
            }

            if (this.sortBy) {
                requestParams.sort_by = this.sortBy;
                requestParams.sort_desc = this.sortDesc;
            }

            // Debug: Log the request parameters
            console.log('Request parameters:', requestParams);

            axios.get('checklist-vehicle/scan-vehicle-security/list', { params: requestParams })
                .then((response) => {
                    if (response.data && response.data.success) {
                        this.datas = response.data.data.data || [];
                        this.totalRows = response.data.data.total || 0;

                        // Add originalIndex for row numbering
                        this.datas.forEach((item, index) => {
                            item.originalIndex = (this.currentPage - 1) * this.currentPerPage + index;
                        });
                    } else {
                        this.datas = [];
                        this.totalRows = 0;
                        console.warn('Unexpected response format:', response.data);
                    }
                })
                .catch((error) => {
                    console.error('Error fetching data:', error);
                    this.datas = [];
                    this.totalRows = 0;

                    if (this.$flashMessage) {
                        this.$flashMessage.error({
                            message: error.response?.data?.message || 'Error loading data',
                            time: 5000,
                        });
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        debounceSearch: debounce(function() {
            this.getData({
                currentPage: 1,
                currentPerPage: this.currentPerPage,
                sortBy: this.sortBy,
                sortDesc: this.sortDesc,
            });
        }, 500),

        loadFilterOptions() {
            // Load companies
            axios.get('company')
                .then((response) => {
                    this.companies = Array.isArray(response.data) ? response.data : [];
                })
                .catch((error) => {
                    console.error('Error loading companies:', error);
                    this.companies = [];
                });

            // Load departments
            axios.get('department')
                .then((response) => {
                    this.departments = Array.isArray(response.data) ? response.data : [];
                })
                .catch((error) => {
                    console.error('Error loading departments:', error);
                    this.departments = [];
                });

            // Load locations
            axios.get('master-location')
                .then((response) => {
                    this.locations = Array.isArray(response.data) ? response.data : [];
                })
                .catch((error) => {
                    console.error('Error loading locations:', error);
                    this.locations = [];
                });
        },

        showFilterModal() {
            this.$bvModal.show('filterModal');
        },

        onCompanyChange() {
            this.updateFilterIds();
        },

        onDepartmentChange() {
            this.updateFilterIds();
        },

        onLocationChange() {
            this.updateFilterIds();
        },

        updateFilterIds() {
            // Update filter arrays with selected IDs
            this.filter.company_id = this.selectedCompanies.map(company => company.id);
            this.filter.department_id = this.selectedDepartments.map(department => department.id);
            this.filter.location_id = this.selectedLocations.map(location => location.id);
        },

        applyFilter() {
            this.updateFilterIds();
            this.$bvModal.hide('filterModal');
            this.getData({
                currentPage: 1,
                currentPerPage: this.currentPerPage,
                sortBy: this.sortBy,
                sortDesc: this.sortDesc,
            });
        },

        resetFilter() {
            // Reset selected arrays
            this.selectedCompanies = [];
            this.selectedDepartments = [];
            this.selectedLocations = [];

            // Reset filter object
            this.filter = {
                company_id: [],
                department_id: [],
                location_id: [],
                start_date: '',
                end_date: ''
            };
            this.applyFilter();
        },

        formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleString('id-ID', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
    }
}
</script>

<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
