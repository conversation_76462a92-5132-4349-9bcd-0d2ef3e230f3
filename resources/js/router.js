import Vue from 'vue'

import Router from 'vue-router'
import store from './vuex'
import AdminLayout from './views/fleet/layout/index'

Vue.use(Router)

let router = new Router({
  mode: 'history',
  routes: [
    {
      path: '/login/:user_id?',
      name: 'login',
      component: () => import('./views/login/index.vue'),
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('./views/register/index.vue'),
    },
    {
      path: '/verify/user/:id',
      name: 'verify',
      props: true,
      component: () => import('./views/verify/index.vue'),
    },
    {
      path: '/forgot-password',
      name: 'forgot',
      component: () => import('./views/forgot/index.vue'),
    },
    {
      path: '/reset/:token',
      name: 'reset',
      component: () => import('./views/reset/index.vue'),
    },
    /**
     * Admin routes
     */
    {
      path: '/',
      name: 'admin',
      component: () => import('./views/fleet/dashboard.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // {
    //   path: '/kendaraan',
    //   name: 'kendaraan',
    //   component: () => import('./views/fleet/kendaraan.vue'),
    //   meta: {
    //     requiresAuth: true,
    //     layout: AdminLayout,
    //   },
    // },

    //Dashboard
    {
      path: '/laporan-biaya',
      name: 'laporan-biaya',
      component: () => import('./views/fleet/dashboard/laporan-biaya.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/laporan-pengajuan-return',
      name: 'laporan-pengajuan-return',
      component: () => import('./views/fleet/dashboard/laporan-pengajuan-return.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/laporan-berita-acara',
      name: 'laporan-berita-acara',
      component: () => import('./views/fleet/dashboard/laporan-berita-acara.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/laporan-pengajuan-retur',
      name: 'laporan-pengajuan-retur',
      component: () => import('./views/fleet/dashboard/laporan_pengajuan.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/laporan-bengkel',
      name: 'laporan-bengkel',
      component: () => import('./views/fleet/dashboard/laporan-bengkel.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/laporan-checklist',
      name: 'laporan-checklist',
      component: () => import('./views/fleet/dashboard/laporan-checklist.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/laporan-pemusnahan',
      name: 'laporan-pemusnahan',
      component: () => import('./views/fleet/dashboard/laporan-pemusnahan.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/laporan-aset',
      name: 'laporan-aset',
      component: () => import('./views/fleet/dashboard/laporan-aset.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/laporan-non-aset',
      name: 'laporan-non-aset',
      component: () => import('./views/fleet/dashboard/laporan-non-aset.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/laporan-stock-opname',
      name: 'laporan-stock-opname',
      component: () => import('./views/fleet/dashboard/laporan-stock-opname.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // MANAGE USER
    {
      path: '/user',
      name: 'manage-user',
      component: () => import('./views/fleet/user/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/user/create',
      name: 'create-user',
      component: () => import('./views/fleet/user/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/user/edit/:id',
      name: 'edit-user',
      component: () => import('./views/fleet/user/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // UNIT OF MEASURE
    {
      path: '/unit-of-measure',
      name: 'unit-of-measure',
      component: () => import('./views/fleet/uom/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/unit-of-measure/create',
      name: 'unit-of-measure-create',
      component: () => import('./views/fleet/uom/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/unit-of-measure/edit/:id',
      name: 'unit-of-measure-edit',
      component: () => import('./views/fleet/uom/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // GROUP BARANG
    {
      path: '/group-barang',
      name: 'group-barang',
      component: () => import('./views/fleet/group_barang/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/group-barang/create',
      name: 'group-barang-create',
      component: () => import('./views/fleet/group_barang/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/group-barang/edit/:id',
      name: 'group-barang-edit',
      component: () => import('./views/fleet/group_barang/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // CLASS BARANG
    {
      path: '/class-barang',
      name: 'class-barang',
      component: () => import('./views/fleet/class_barang/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/class-barang/create',
      name: 'class-barang-create',
      component: () => import('./views/fleet/class_barang/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/class-barang/edit/:id',
      name: 'class-barang-edit',
      component: () => import('./views/fleet/class_barang/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // LOCATION MAINTENANCE
    {
      path: '/location-maintenance',
      name: 'location-maintenance',
      component: () => import('./views/fleet/location_maintenance/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/location-maintenance/create',
      name: 'location-maintenance-create',
      component: () => import('./views/fleet/location_maintenance/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/location-maintenance/edit/:id',
      name: 'location-maintenance-edit',
      component: () => import('./views/fleet/location_maintenance/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // STOCK CATEGORY
    {
      path: '/stock-category',
      name: 'stock-category',
      component: () => import('./views/fleet/stock_category/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/stock-category/create',
      name: 'stock-category-create',
      component: () => import('./views/fleet/stock_category/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/stock-category/edit/:id',
      name: 'stock-category-edit',
      component: () => import('./views/fleet/stock_category/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // ITEM MAINTENANCE
    {
      path: '/item-maintenance',
      name: 'item-maintenance',
      component: () => import('./views/fleet/item_maintenance/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/item-maintenance/create',
      name: 'item-maintenance-create',
      component: () => import('./views/fleet/item_maintenance/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/item-maintenance/edit/:id',
      name: 'item-maintenance-edit',
      component: () => import('./views/fleet/item_maintenance/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/divisi',
      name: 'divisi',
      component: () => import('./views/fleet/divisi/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/divisi/create',
      name: 'divisi-create',
      component: () => import('./views/fleet/divisi/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/divisi/edit/:id',
      name: 'divisi-edit',
      component: () => import('./views/fleet/divisi/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/department',
      name: 'department',
      component: () => import('./views/fleet/department/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/department/create',
      name: 'department-create',
      component: () => import('./views/fleet/department/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/department/edit/:id',
      name: 'department-edit',
      component: () => import('./views/fleet/department/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/section',
      name: 'section',
      component: () => import('./views/fleet/section/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/section/create',
      name: 'section-create',
      component: () => import('./views/fleet/section/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/section/edit/:id',
      name: 'section-edit',
      component: () => import('./views/fleet/section/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/sub-section',
      name: 'sub-section',
      component: () => import('./views/fleet/sub_section/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/sub-section/create',
      name: 'sub-section-create',
      component: () => import('./views/fleet/sub_section/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/sub-section/edit/:id',
      name: 'sub-section-edit',
      component: () => import('./views/fleet/sub_section/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/currencies',
      name: 'currencies',
      component: () => import('./views/fleet/currencies/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/currencies/create',
      name: 'currencies-create',
      component: () => import('./views/fleet/currencies/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/currencies/edit/:id',
      name: 'currencies-edit',
      component: () => import('./views/fleet/currencies/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    //company
    {
      path: '/company',
      name: 'company',
      component: () => import('./views/fleet/company/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/company/create',
      name: 'company-create',
      component: () => import('./views/fleet/company/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/company/edit/:id',
      name: 'company-edit',
      component: () => import('./views/fleet/company/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/supplier-category',
      name: 'supplier-category',
      component: () => import('./views/fleet/supplier_category/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/supplier-category/create',
      name: 'supplier-category-create',
      component: () => import('./views/fleet/supplier_category/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/supplier-category/edit/:id',
      name: 'supplier-category-edit',
      component: () => import('./views/fleet/supplier_category/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/supplier-type',
      name: 'supplier-type',
      component: () => import('./views/fleet/supplier_type/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/supplier-type/create',
      name: 'supplier-type-create',
      component: () => import('./views/fleet/supplier_type/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/supplier-type/edit/:id',
      name: 'supplier-type-edit',
      component: () => import('./views/fleet/supplier_type/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/supplier',
      name: 'supplier',
      component: () => import('./views/fleet/supplier/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/supplier/create',
      name: 'supplier-create',
      component: () => import('./views/fleet/supplier/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/supplier/edit/:id',
      name: 'supplier-edit',
      component: () => import('./views/fleet/supplier/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/shipper',
      name: 'shipper',
      component: () => import('./views/fleet/shipper/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/shipper/create',
      name: 'shipper-create',
      component: () => import('./views/fleet/shipper/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/shipper/edit/:id',
      name: 'shipper-edit',
      component: () => import('./views/fleet/shipper/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/role',
      name: 'role',
      component: () => import('./views/fleet/role/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/role/create',
      name: 'role-create',
      component: () => import('./views/fleet/role/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/role/edit/:id',
      name: 'role-edit',
      component: () => import('./views/fleet/role/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/role-user',
      name: 'role-user',
      component: () => import('./views/fleet/role_user/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/role-user/create',
      name: 'role-user-create',
      component: () => import('./views/fleet/role_user/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/role-user/edit/:id',
      name: 'role-user-edit',
      component: () => import('./views/fleet/role_user/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/system-setting',
      name: 'system-setting',
      component: () => import('./views/fleet/system_setting/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/payment-term',
      name: 'payment-term',
      component: () => import('./views/fleet/payment_term/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/payment-term/create',
      name: 'create-payment-term',
      component: () => import('./views/fleet/payment_term/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/payment-term/edit/:id',
      name: 'edit-payment-term',
      component: () => import('./views/fleet/payment_term/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/payment-methods',
      name: 'payment-methods',
      component: () => import('./views/fleet/payment_methods/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/payment-methods/create',
      name: 'create-payment-methods',
      component: () => import('./views/fleet/payment_methods/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/payment-methods/edit/:id',
      name: 'edit-payment-methods',
      component: () => import('./views/fleet/payment_methods/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/discount-category',
      name: 'discount-category',
      component: () => import('./views/fleet/discount_category/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/discount-category/create',
      name: 'create-discount-category',
      component: () => import('./views/fleet/discount_category/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/discount-category/edit/:id',
      name: 'edit-discount-category',
      component: () => import('./views/fleet/discount_category/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/branch',
      name: 'branch',
      component: () => import('./views/fleet/branch/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/branch/create',
      name: 'create-branch',
      component: () => import('./views/fleet/branch/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/branch/edit/:id',
      name: 'edit-branch',
      component: () => import('./views/fleet/branch/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // Merk Vehicle
    {
      path: '/master/merk',
      component: () => import('./views/fleet/master'),
      redirect: 'master/merk/list',
      children: [
        {
          name: 'merk',
          path: 'list',
          component: () => import('./views/fleet/master/merk.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          name: 'merk_create',
          path: 'create',
          component: () => import('./views/fleet/master/merk_create.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          name: 'merk_update',
          path: 'edit/:id',
          component: () => import('./views/fleet/master/update_merk.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
      ],
    },

    // Color Vehicle
    {
      path: '/master/color',
      component: () => import('./views/fleet/master'),
      redirect: 'master/color/list',
      children: [
        {
          name: 'color',
          path: 'list',
          component: () => import('./views/fleet/master/color.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          name: 'color_create',
          path: 'create',
          component: () => import('./views/fleet/master/color_create.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          name: 'color_update',
          path: 'edit/:id',
          component: () => import('./views/fleet/master/color_update.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
      ],
    },

    //   Engine Vehicle
    {
      path: '/master/engine',
      component: () => import('./views/fleet/master'),
      redirect: 'master/engine/list',
      children: [
        {
          name: 'engine',
          path: 'list',
          component: () => import('./views/fleet/master/engine.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          name: 'engine_create',
          path: 'create',
          component: () => import('./views/fleet/master/engine_create.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          name: 'engine_update',
          path: 'edit/:id',
          component: () => import('./views/fleet/master/engine_update.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
      ],
    },

    //   Category Vehicle
    {
      path: '/master/category',
      component: () => import('./views/fleet/master'),
      redirect: 'master/category/list',
      children: [
        {
          name: 'category',
          path: 'list',
          component: () => import('./views/fleet/master/category.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          name: 'category_create',
          path: 'create',
          component: () => import('./views/fleet/master/category_create.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          name: 'category_update',
          path: 'edit/:id',
          component: () => import('./views/fleet/master/category_update.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
      ],
    },

    // Data Kendaraan
    {
      path: '/kendaraan',
      component: () => import('./views/fleet/kendaraan'),
      redirect: 'kendaraan/list',
      children: [
        {
          name: 'kendaraan_list',
          path: 'list',
          component: () => import('./views/fleet/kendaraan/list.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        // VENDOR
        {
          path: '/vendor',
          name: 'vendor',
          component: () => import('./views/fleet/vendor/index.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          path: '/vendor/create',
          name: 'vendor-create',
          component: () => import('./views/fleet/vendor/create.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          path: '/vendor/edit/:id',
          name: 'vendor-edit',
          component: () => import('./views/fleet/vendor/edit.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        // VEHICLE MASTER
        {
          name: 'kendaraan_create',
          path: 'create',
          component: () => import('./views/fleet/kendaraan/create.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          name: 'kendaraan_update',
          path: 'edit/:id',
          component: () => import('./views/fleet/kendaraan/update.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
      ],
    },

    {
      path: "/kendaraan/km-actual/:id",
      name: "kendaraan-km-actual",
      component: () => import("./views/fleet/kendaraan/km_actual.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/kendaraan/kir/:id",
      name: "kendaraan-kir",
      component: () => import("./views/fleet/kendaraan/kir.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/kendaraan/service/:id",
      name: "kendaraan-service",
      component: () => import("./views/fleet/kendaraan/service.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },

    // Data Driver
    {
      path: '/driver',
      name: 'manage-driver',
      component: () => import('./views/fleet/driver/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/driver/create',
      name: 'create-driver',
      component: () => import('./views/fleet/driver/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/driver/edit/:id',
      name: 'edit-driver',
      component: () => import('./views/fleet/driver/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },

    // Data Technical
    {
      path: '/technical/list',
      name: 'technical-list',
      component: () => import('./views/fleet/technical/list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/technical/create',
      name: 'technical-create',
      component: () => import('./views/fleet/technical/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/technical/edit/:id',
      name: 'technical-update',
      component: () => import('./views/fleet/technical/update.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },

    // Data Insurance
    {
      path: '/workshop/insurance',
      component: () => import('./views/fleet/workshop'),
      redirect: 'workshop/insurance/list',
      children: [
        {
          name: 'insurance',
          path: 'list',
          component: () => import('./views/fleet/workshop/insurance.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          name: 'insurance_create',
          path: 'create',
          component: () => import('./views/fleet/workshop/insurance_create.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          name: 'insurance_update',
          path: 'edit/:id',
          component: () => import('./views/fleet/workshop/insurance_update.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
      ],
    },

    // Data Pengajuan
    // {
    //   path: '/workshop/pengajuan',
    //   component: () => import('./views/fleet/workshop'),
    //   redirect: 'workshop/bengkel',
    //   children: [
    //     {
    //       name: 'bengkel',
    //       component: () => import('./views/fleet/workshop/bengkel.vue'),
    //       meta: {
    //         requiresAuth: true,
    //         layout: AdminLayout,
    //       },
    //     },
    //     {
    //       name: 'pengajuan_create',
    //       path: 'create',
    //       component: () => import('./views/fleet/workshop/pengajuan_create.vue'),
    //       meta: {
    //         requiresAuth: true,
    //         layout: AdminLayout,
    //       },
    //     },
    //     {
    //       name: 'pengajuan_update',
    //       path: 'edit/:id',
    //       component: () => import('./views/fleet/workshop/pengajuan_update.vue'),
    //       meta: {
    //         requiresAuth: true,
    //         layout: AdminLayout,
    //       },
    //     },
    //     {
    //       name: 'pengajuan_approval',
    //       path: 'approval/:id',
    //       component: () => import('./views/fleet/workshop/pengajuan_approval.vue'),
    //       meta: {
    //         requiresAuth: true,
    //         layout: AdminLayout,
    //       },
    //     },
    //   ],
    // },

    // Data jenis service
    {
      path: '/workshop/service',
      component: () => import('./views/fleet/workshop'),
      redirect: 'workshop/service/list',
      children: [
        {
          name: 'service',
          path: 'list',
          component: () => import('./views/fleet/workshop/service.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          name: 'service_create',
          path: 'create',
          component: () => import('./views/fleet/workshop/service_create.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
        {
          name: 'service_update',
          path: 'edit/:id',
          component: () => import('./views/fleet/workshop/service_update.vue'),
          meta: {
            requiresAuth: true,
            layout: AdminLayout,
          },
        },
      ],
    },
    {
      path: "/keputusan-service",
      name: "keputusan-service",
      component: () => import("./views/fleet/workshop/keputusan_service.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
        path: "/pr-approval-setting",
        name: "pr-approval-setting",
        component: () => import("./views/fleet/pr_approval_setting/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/pr-approval-setting/create",
        name: "pr-approval-setting-create",
        component: () => import("./views/fleet/pr_approval_setting/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/pr-approval-setting/edit/:id",
        name: "pr-approval-setting-edit",
        component: () => import("./views/fleet/pr_approval_setting/edit.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/cq-approval-setting",
        name: "cq-approval-setting",
        component: () => import("./views/fleet/cq_approval_setting/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/cq-approval-setting/create",
        name: "cq-approval-setting-create",
        component: () => import("./views/fleet/cq_approval_setting/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/cq-approval-setting/edit/:id",
        name: "cq-approval-setting-edit",
        component: () => import("./views/fleet/cq_approval_setting/edit.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/po-approval-setting",
        name: "po-approval-setting",
        component: () => import("./views/fleet/po_approval_setting/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/po-approval-setting/create",
        name: "po-approval-setting-create",
        component: () => import("./views/fleet/po_approval_setting/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/po-approval-setting/edit/:id",
        name: "po-approval-setting-edit",
        component: () => import("./views/fleet/po_approval_setting/edit.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/wo-approval-setting",
        name: "wo-approval-setting",
        component: () => import("./views/fleet/wo_approval_setting/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/wo-approval-setting/create",
        name: "wo-approval-setting-creae",
        component: () => import("./views/fleet/wo_approval_setting/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/wo-approval-setting/edit/:id",
        name: "wo-approval-setting-edit",
        component: () => import("./views/fleet/wo_approval_setting/edit.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/purchase-request",
        name: "purchase-request",
        component: () => import("./views/fleet/purchase_request/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/purchase-request/create",
        name: "purchase-request-create",
        component: () => import("./views/fleet/purchase_request/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/purchase-request/edit/:id",
        name: "purchase-request-edit",
        component: () => import("./views/fleet/purchase_request/edit.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
      path: "/purchase-request/pickup/:id",
      name: "purchase-request-pickup",
      component: () => import("./views/fleet/purchase_request/pickup.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-request/item-select/:id",
      name: "purchase-request-item-select",
      component: () => import("./views/fleet/purchase_request/item_select.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
        path: "/approval-purchase-request",
        name: "approval-purchase-request",
        component: () => import("./views/fleet/approval_purchase_request/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/approval-purchase-request/edit/:id",
        name: "approval-purchase-request-edit",
        component: () => import("./views/fleet/approval_purchase_request/edit.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
      path: '/purchase-request/list',
      name: 'purchase-request-list',
      component: () => import('./views/fleet/purchase_request/list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/purchase-request/approval_ga',
      name: 'purchase-request-approval_ga',
      component: () => import('./views/fleet/purchase_request/approval_ga.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/purchase-request/approval_ga/:id',
      name: 'purchase-request-update-approval_ga',
      component: () => import('./views/fleet/purchase_request/update_approval_ga.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/purchase-request/approval_direktur',
      name: 'purchase-request-approval_direktur',
      component: () => import('./views/fleet/purchase_request/approval_direktur.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/purchase-request/approval_direktur/:id',
      name: 'purchase-request-update-approval_direktur',
      component: () => import('./views/fleet/purchase_request/update_approval_direktur.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
        path: "/compare-purchase-request",
        name: "compare-purchase-request",
        component: () => import("./views/fleet/compare_purchase_request/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/compare-purchase-request/supplier",
        name: "compare-purchase-request",
        component: () => import("./views/fleet/compare_purchase_request/supplier.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
      path: "/compare-purchase-request/edit/:id",
      name: "compare-purchase-request-edit",
      component: () => import("./views/fleet/compare_purchase_request/edit.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/compare-purchase-request/show/:id",
      name: "compare-purchase-request-show",
      component: () => import("./views/fleet/compare_purchase_request/show.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/pickup-purchase-request",
      name: "pickup-purchase-request",
      component: () => import("./views/fleet/pickup_purchase_request/index.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
        path: "/pickup-purchase-request/create",
        name: "pickup-purchase-request-create",
        component: () => import("./views/fleet/pickup_purchase_request/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/pickup-purchase-request/show/:id",
        name: "pickup-purchase-request-show",
        component: () => import("./views/fleet/pickup_purchase_request/show.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
      path: "/comparative-quotation-approval",
      name: "comparative-quotation-approval",
      component: () => import("./views/fleet/comparative_quotation_approval/index.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/comparative-quotation-approval/show/:id",
      name: "comparative-quotation-approval-show",
      component: () => import("./views/fleet/comparative_quotation_approval/show.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order",
      name: "purchase-order",
      component: () => import("./views/fleet/purchase_order/index.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/payment-request",
      name: "payment-request",
      component: () => import("./views/fleet/purchase_order/index_pr.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order-add",
      name: "purchase-order-add",
      component: () => import("./views/fleet/purchase_order/create_list.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order/:id",
      name: "purchase-order-show",
      component: () => import("./views/fleet/purchase_order/show.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order/show-po/:id",
      name: "purchase-order-show",
      component: () => import("./views/fleet/purchase_order/show_po.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    // {
    //   path: "/purchase-order/create/:id/:supplier",
    //   name: "purchase-order-create",
    //   component: () => import("./views/fleet/purchase_order/create.vue"),
    //   meta: {
    //       requiresAuth: true,
    //       layout: AdminLayout
    //   }
    // },
    {
      path: "/purchase-order-create",
      name: "purchase-order-create-po",
      component: () => import("./views/fleet/purchase_order/create_po.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order-create-pr",
      name: "purchase-order-create-pr",
      component: () => import("./views/fleet/purchase_order/create_pr.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order-approval-rsc",
      name: "purchase-order-approval-rsc",
      component: () => import("./views/fleet/purchase_order/approval_rsc.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order-approval-rsc/:id",
      name: "purchase-order-update-rsc",
      component: () => import("./views/fleet/purchase_order/update_po_rsc.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order-approval-direktur",
      name: "purchase-order-approval-direktur",
      component: () => import("./views/fleet/purchase_order/approval_direktur.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/payment-request-approval-rsc",
      name: "payment-request-approval-rsc",
      component: () => import("./views/fleet/purchase_order/approval_pr_rsc.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/payment-request-approval-rsc/:id",
      name: "payment-request-update-rsc",
      component: () => import("./views/fleet/purchase_order/update_pr_rsc.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/payment-request-approval-finance",
      name: "payment-request-approval-finance",
      component: () => import("./views/fleet/purchase_order/approval_pr_finance.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/payment-request-approval-direktur",
      name: "payment-request-approval-direktur",
      component: () => import("./views/fleet/purchase_order/approval_pr_direktur.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order-approval",
      name: "purchase-order-approval",
      component: () => import("./views/fleet/purchase_order/approval_list.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order-receive",
      name: "purchase-order-receive",
      component: () => import("./views/fleet/purchase_order/receive_list.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order-receive/show/:id",
      name: "purchase-order-receive-show",
      component: () => import("./views/fleet/purchase_order/receive_show.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order-receive/store/:id",
      name: "purchase-order-receive-store",
      component: () => import("./views/fleet/purchase_order/receive_store.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order-receive/penomoran/:id",
      name: "purchase-order-receive-penomoran",
      component: () => import("./views/fleet/purchase_order/receive_penomoran.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order-payment",
      name: "purchase-order-payment",
      component: () => import("./views/fleet/purchase_order/payment_list.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/purchase-order-payment/:id/list",
      name: "purchase-order-payment-list",
      component: () => import("./views/fleet/purchase_order/payment_list_detail.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    // GOOD RECEIVED
    {
      path: "/good_received-po",
      name: "good_received-po",
      component: () => import("./views/fleet/good_received/good_received_po.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/good-received-po/:id",
      name: "good-received-po/:id",
      component: () => import("./views/fleet/good_received/update_gr_po.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/good_received-pr",
      name: "good_received-pr",
      component: () => import("./views/fleet/good_received/good_received_pr.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/good-received-pr/:id",
      name: "good-received-pr/:id",
      component: () => import("./views/fleet/good_received/update_gr_pr.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/list-gr",
      name: "list-gr",
      component: () => import("./views/fleet/good_received/list_gr.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/sort-gr-non-asset",
      name: "sort-gr-non-asset",
      component: () => import("./views/fleet/good_received/sort_gr_non_asset.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/received-handover-non-asset",
      name: "received-handover-non-asset",
      component: () => import("./views/fleet/good_received/received_handover_non_asset.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/list-handover-non-asset",
      name: "list-handover-non-asset",
      component: () => import("./views/fleet/good_received/list_handover_non_asset.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/handover-create",
      name: "handover-create",
      component: () => import("./views/fleet/good_received/create_hn.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    // VENDOR CATEGORY
    {
      path: '/vendor-category',
      name: 'vendor-category',
      component: () => import('./views/fleet/vendor_category/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/vendor-category/create',
      name: 'vendor-category-create',
      component: () => import('./views/fleet/vendor_category/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/vendor-category/edit/:id',
      name: 'vendor-category-edit',
      component: () => import('./views/fleet/vendor_category/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // VENDOR TYPE
    {
      path: '/vendor-type',
      name: 'vendor-type',
      component: () => import('./views/fleet/vendor_type/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/vendor-type/create',
      name: 'vendor-type-create',
      component: () => import('./views/fleet/vendor_type/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // TAX CATEGORY
    {
      path: '/tax-category',
      name: 'tax-category',
      component: () => import('./views/fleet/tax_category/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/tax-category/create',
      name: 'tax-category-create',
      component: () => import('./views/fleet/tax_category/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/tax-category/edit/:id',
      name: 'tax-category-edit',
      component: () => import('./views/fleet/tax_category/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // TAX GROUP
    {
      path: '/tax-group',
      name: 'tax-group',
      component: () => import('./views/fleet/tax_group/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/tax-group/create',
      name: 'tax-group-create',
      component: () => import('./views/fleet/tax_group/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/tax-group/edit/:id',
      name: 'tax-group-edit',
      component: () => import('./views/fleet/tax_group/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // TAX AUTHORITY
    {
      path: '/tax-authority',
      name: 'tax-authority',
      component: () => import('./views/fleet/tax_authority/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/tax-authority/create',
      name: 'tax-authority-create',
      component: () => import('./views/fleet/tax_authority/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/tax-authority/edit/:id',
      name: 'tax-authority-edit',
      component: () => import('./views/fleet/tax_authority/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // QUESTION
    {
      path: '/question',
      name: 'question',
      component: () => import('./views/fleet/question/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/question/type/:type',
      name: 'question',
      component: () => import('./views/fleet/question/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/question/create',
      name: 'question-create',
      component: () => import('./views/fleet/question/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/question/edit/:id',
      name: 'question-edit',
      component: () => import('./views/fleet/question/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // CATEGORY QUESTION
    {
      path: '/category-question',
      name: 'category-question',
      component: () => import('./views/fleet/category_question/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/category-question/create',
      name: 'category-question-create',
      component: () => import('./views/fleet/category_question/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/category-question/edit/:id',
      name: 'category-question-edit',
      component: () => import('./views/fleet/category_question/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // DELIVER
    {
      path: '/deliver',
      name: 'deliver',
      component: () => import('./views/fleet/deliver/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/deliver/create',
      name: 'deliver-create',
      component: () => import('./views/fleet/deliver/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/deliver/edit/:id',
      name: 'deliver-edit',
      component: () => import('./views/fleet/deliver/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // INVOICE ADDRESS
    {
      path: '/invoice-send',
      name: 'invoice-send',
      component: () => import('./views/fleet/invoice_send/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/invoice-send/create',
      name: 'invoice-send-create',
      component: () => import('./views/fleet/invoice_send/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/invoice-send/edit/:id',
      name: 'invoice-send-edit',
      component: () => import('./views/fleet/invoice_send/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // LOKASI PEMUSNAHAN
    {
      path: '/location-extermination',
      name: 'location-extermination',
      component: () => import('./views/fleet/location_extermination/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/location-extermination/create',
      name: 'location-extermination-create',
      component: () => import('./views/fleet/location_extermination/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/location-extermination/edit/:id',
      name: 'location-extermination-edit',
      component: () => import('./views/fleet/location_extermination/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // MASTER TYPE ASSET
    {
      path: '/master-type-asset',
      name: 'master-type-asset',
      component: () => import('./views/fleet/master_type_asset/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/master-type-asset/create',
      name: 'master-type-asset-create',
      component: () => import('./views/fleet/master_type_asset/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/master-type-asset/edit/:id',
      name: 'master-type-asset-edit',
      component: () => import('./views/fleet/master_type_asset/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // CATEGORY ITEM
    {
      path: '/category-item',
      name: 'category-item',
      component: () => import('./views/fleet/category_item/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/category-item/create',
      name: 'category-item-create',
      component: () => import('./views/fleet/category_item/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/category-item/edit/:id',
      name: 'category-item-edit',
      component: () => import('./views/fleet/category_item/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // TYPE ITEM
    {
      path: '/type-item',
      name: 'type-item',
      component: () => import('./views/fleet/type_item/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/type-item/create',
      name: 'type-item-create',
      component: () => import('./views/fleet/type_item/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/type-item/edit/:id',
      name: 'type-item-edit',
      component: () => import('./views/fleet/type_item/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // PEMUSNAHAN BARANG BEKAS
    {
      path: '/waste-disposal/create',
      name: 'waste-disposal-create',
      component: () => import('./views/fleet/waste_disposal/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/waste-disposal',
      name: 'waste-disposal',
      component: () => import('./views/fleet/waste_disposal/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/pengajuan-pemusnahan',
      name: 'pengajuan-pemusnahan',
      component: () => import('./views/fleet/waste_disposal/pengajuan_pemusnahan.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/pengajuan-pemusnahan/create',
      name: 'pengajuan-pemusnahan-create',
      component: () => import('./views/fleet/waste_disposal/pengajuan_pemusnahan_create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/berita-acara',
      name: 'berita-acara',
      component: () => import('./views/fleet/waste_disposal/berita_acara.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/berita-acara/:id',
      name: 'berita-acara-create',
      component: () => import('./views/fleet/waste_disposal/berita_acara_create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/list-pemusnahan',
      name: 'list-pemusnahan',
      component: () => import('./views/fleet/waste_disposal/list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // RETUR
    {
      path: '/retur/create',
      name: 'retur-create',
      component: () => import('./views/fleet/retur/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/retur',
      name: 'retur',
      component: () => import('./views/fleet/retur/list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // Warehouse
    {
      path: '/warehouse',
      name: 'warehouse',
      component: () => import('./views/fleet/warehouse/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/warehouse/create',
      name: 'warehouse-create',
      component: () => import('./views/fleet/warehouse/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/warehouse/edit/:id',
      name: 'warehouse-edit',
      component: () => import('./views/fleet/warehouse/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // RACKING
    {
      path: '/racking',
      name: 'racking',
      component: () => import('./views/fleet/racking/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/racking/create',
      name: 'racking-create',
      component: () => import('./views/fleet/racking/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/racking/edit/:id',
      name: 'racking-edit',
      component: () => import('./views/fleet/racking/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/approval-racking',
      name: 'approval-racking',
      component: () => import('./views/fleet/approval_racking/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/racking/pairing',
      name: 'racking-pairing',
      component: () => import('./views/fleet/racking/pairing.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/racking/pairing/create',
      name: 'racking-pairing-create',
      component: () => import('./views/fleet/racking/create_pairing.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/racking/pairing/:id',
      name: 'racking-pairing-update',
      component: () => import('./views/fleet/racking/pairing_update.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // Request Form
    {
      path: '/request-form',
      name: 'request-form',
      component: () => import('./views/fleet/request_form/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/request-form/create',
      name: 'request-form-create',
      component: () => import('./views/fleet/request_form/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/request-form/edit/:id',
      name: 'request-form-edit',
      component: () => import('./views/fleet/request_form/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/request-form/approval',
      name: 'request-form-approval',
      component: () => import('./views/fleet/request_form/approval.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/request-form/approval/:id',
      name: 'request-form-update-approval',
      component: () => import('./views/fleet/request_form/update_approval.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/request-form/verifikasi-rf',
      name: 'request-form-verifikasi-rf',
      component: () => import('./views/fleet/request_form/verifikasi_rf.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/request-form/verifikasi-rf/:id',
      name: 'request-form-update-verifikasi-rf',
      component: () => import('./views/fleet/request_form/update_verifikasi_rf.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // WORK ESTIMATE
    {
      path: '/work-estimate',
      name: 'work-estimate',
      component: () => import('./views/fleet/work_estimate/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/work-estimate/create',
      name: 'work-estimate-create',
      component: () => import('./views/fleet/work_estimate/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/work-estimate/edit/:id',
      name: 'work-estimate-edit',
      component: () => import('./views/fleet/work_estimate/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // LOCATION CHEKLIST
    {
      path: '/location-checklist',
      name: 'location-checklist',
      component: () => import('./views/fleet/location_checklist/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/location-checklist/create',
      name: 'location-checklist-create',
      component: () => import('./views/fleet/location_checklist/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/location-checklist/edit/:id',
      name: 'location-checklist-edit',
      component: () => import('./views/fleet/location_checklist/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // K3
    {
      path: '/k3',
      name: 'k3',
      component: () => import('./views/fleet/k3/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/k3/create',
      name: 'k3-create',
      component: () => import('./views/fleet/k3/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/k3/edit/:id',
      name: 'k3-edit',
      component: () => import('./views/fleet/k3/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/report-k3',
      name: 'report-k3',
      component: () => import('./views/fleet/k3/report.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // TYPE SIM
    {
      path: '/type-sim',
      name: 'type-sim',
      component: () => import('./views/fleet/type_sim/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/type-sim/create',
      name: 'type-sim-create',
      component: () => import('./views/fleet/type_sim/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/type-sim/edit/:id',
      name: 'type-sim-edit',
      component: () => import('./views/fleet/type_sim/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // MASTER LOKASI
    {
      path: '/master-location',
      name: 'master-location',
      component: () => import('./views/fleet/master_location/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/master-location/create',
      name: 'master-location-create',
      component: () => import('./views/fleet/master_location/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/master-location/edit/:id',
      name: 'master-location-edit',
      component: () => import('./views/fleet/master_location/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // STATUS PERBAIKAN
    {
      path: '/status-perbaikan',
      name: 'status-perbaikan',
      component: () => import('./views/fleet/status_perbaikan/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/status-perbaikan/create',
      name: 'status-perbaikan-create',
      component: () => import('./views/fleet/status_perbaikan/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/status-perbaikan/edit/:id',
      name: 'status-perbaikan-edit',
      component: () => import('./views/fleet/status_perbaikan/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // MASTER VIDIO
    {
      path: '/master-vidio',
      name: 'master-vidio',
      component: () => import('./views/fleet/master_vidio/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/master-vidio/create',
      name: 'master-vidio-create',
      component: () => import('./views/fleet/master_vidio/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/master-vidio/edit/:id',
      name: 'master-vidio-edit',
      component: () => import('./views/fleet/master_vidio/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // KATEGORI KERUSAKAN
    {
      path: '/kategori-kerusakan',
      name: 'kategori-kerusakan',
      component: () => import('./views/fleet/kategori_kerusakan/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/kategori-kerusakan/create',
      name: 'kategori-kerusakan-create',
      component: () => import('./views/fleet/kategori_kerusakan/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/kategori-kerusakan/edit/:id',
      name: 'kategori-kerusakan-edit',
      component: () => import('./views/fleet/kategori_kerusakan/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // STATUS VEHICLE
    {
      path: '/status-vehicle',
      name: 'status-vehicle',
      component: () => import('./views/fleet/status_vehicle/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/status-vehicle/create',
      name: 'status-vehicle-create',
      component: () => import('./views/fleet/status_vehicle/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/status-vehicle/edit/:id',
      name: 'status-vehicle-edit',
      component: () => import('./views/fleet/status_vehicle/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // CHECKLIST VEHICLE
    {
      path: '/checklist-vehicle',
      name: 'checklist-vehicle',
      component: () => import('./views/fleet/checklist_vehicle/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
        path: '/reset-checklist/checklist-vehicle',
        name: 'checklist-vehicle-reset-checklist',
        component: () => import('./views/fleet/checklist_vehicle/reset_checklist.vue'),
        meta: {
          requiresAuth: true,
          layout: AdminLayout,
        },
      },
    {
      path: '/checklist-vehicle/create/:id',
      name: 'checklist-vehicle-create',
      component: () => import('./views/fleet/checklist_vehicle/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/checklist-vehicle/:id',
      name: 'checklist-vehicle',
      component: () => import('./views/fleet/checklist_vehicle/history.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/report-checklist-vehicle',
      name: 'report-checklist-vehicle',
      component: () => import('./views/fleet/checklist_vehicle/report.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/scan-security-history',
      name: 'scan-security-history',
      component: () => import('./views/fleet/scan_security/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // CHECKLIST HEALTH
    {
      path: '/checklist-health',
      name: 'checklist-health',
      component: () => import('./views/fleet/checklist_health/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/checklist-health/create',
      name: 'checklist-health-create',
      component: () => import('./views/fleet/checklist_health/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/checklist-health/:id',
      name: 'checklist-health',
      component: () => import('./views/fleet/checklist_health/history.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },

    //INVENTORY STOCK
    {
      path: '/inventory-stock/list',
      name: 'inventory-stock-list',
      component: () => import('./views/fleet/inventory_stock/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/inventory-stock/transfer',
      name: 'inventory-stock-transfer',
      component: () => import('./views/fleet/inventory_stock/transfer_list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/inventory-stock/transfer/create',
      name: 'inventory-stock-transfer-create',
      component: () => import('./views/fleet/inventory_stock/transfer_create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/inventory-stock/transfer/create/:id',
      name: 'inventory-stock-transfer-create',
      component: () => import('./views/fleet/inventory_stock/transfer_create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    //ASSETS
    {
      path: '/assets/list',
      name: 'assets-list',
      component: () => import('./views/fleet/assets/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/assets/create',
      name: 'assets-create',
      component: () => import('./views/fleet/assets/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/assets/list2',
      name: 'assets-list2',
      component: () => import('./views/fleet/assets/index2.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/assets/edit/:id',
      name: 'assets-edit',
      component: () => import('./views/fleet/assets/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/assets/log/:id',
      name: 'assets-log',
      component: () => import('./views/fleet/assets/log.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // GROUP ASSET
    {
      path: '/group-asset',
      name: 'group-asset',
      component: () => import('./views/fleet/group_asset/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/group-asset/create',
      name: 'group-asset-create',
      component: () => import('./views/fleet/group_asset/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/group-asset/edit/:id',
      name: 'group-asset-edit',
      component: () => import('./views/fleet/group_asset/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // CLASS BARANG
    {
      path: '/class-asset',
      name: 'class-asset',
      component: () => import('./views/fleet/class_asset/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/class-asset/create',
      name: 'class-asset-create',
      component: () => import('./views/fleet/class_asset/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/class-asset/edit/:id',
      name: 'class-asset-edit',
      component: () => import('./views/fleet/class_asset/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    //pemusnahan barang
    {
      path: '/asset-destruction/list',
      name: 'asset-destruction-list',
      component: () => import('./views/fleet/asset_destruction/index.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/asset-destruction/create',
      name: 'asset-destruction-create',
      component: () => import('./views/fleet/asset_destruction/create.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/asset-destruction/edit/:id',
      name: 'asset-destruction-edit',
      component: () => import('./views/fleet/asset_destruction/edit.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/asset-destruction/approve',
      name: 'asset-destruction-approve',
      component: () => import('./views/fleet/asset_destruction/approve.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/asset-destruction/show/:id',
      name: 'asset-destruction-show',
      component: () => import('./views/fleet/asset_destruction/show.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    //asset descrution approval setting
    {
        path: "/destruction-approval-setting",
        name: "destruction-approval-setting",
        component: () => import("./views/fleet/destruction_approval_setting/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/destruction-approval-setting/create",
        name: "destruction-approval-setting-create",
        component: () => import("./views/fleet/destruction_approval_setting/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/destruction-approval-setting/edit/:id",
        name: "destruction-approval-setting-edit",
        component: () => import("./views/fleet/destruction_approval_setting/edit.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    //asset destruction income
    {
        path: "/asset-destruction-income",
        name: "asset-destruction-income",
        component: () => import("./views/fleet/destruction_income/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset-destruction-income/create",
        name: "asset-destruction-income-create",
        component: () => import("./views/fleet/destruction_income/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset-destruction-income/edit/:id",
        name: "asset-destruction-income-edit",
        component: () => import("./views/fleet/destruction_income/edit.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
      path: "/workshop/ga-ho",
      name: "workshop/ga-ho",
      component: () => import("./views/fleet/workshop/list_gaho.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/workshop/gaho",
      name: "workshop/gaho",
      component: () => import("./views/fleet/workshop/gaho.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/workshop/bengkel-internal",
      name: "workshop/bengkel-internal",
      component: () => import("./views/fleet/workshop/bengkel_internal.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/workshop/bengkel-external",
      name: "workshop/bengkel-external",
      component: () => import("./views/fleet/workshop/bengkel_external.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/workshop/report",
      name: "workshop/report",
      component: () => import("./views/fleet/workshop/report.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/workshop/pemeriksaan-pengerjaan",
      name: "workshop/pemeriksaan-pengerjaan",
      component: () => import("./views/fleet/workshop/pemeriksaan_pengerjaan.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/workshop/keputusan-akhir-gaho",
      name: "workshop/keputusan-akhir-gaho",
      component: () => import("./views/fleet/workshop/keputusan_akhir_gaho.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/workshop/keputusan-akhir-kordi",
      name: "workshop/keputusan-akhir-kordi",
      component: () => import("./views/fleet/workshop/keputusan_akhir_kordi.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
        path: "/workshop/reset-kendaraan",
        name: "workshop-reset-kendaraan",
        component: () => import("./views/fleet/workshop/reset_kendaraan.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
      },
    {
      path: "/accident",
      name: "accident",
      component: () => import("./views/fleet/accident/index.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/claim-insurance",
      name: "claim-insurance",
      component: () => import("./views/fleet/claim_insurance/index.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    //stock opname
    {
        path: "/stock-opname/list",
        name: "stock-opname-list",
        component: () => import("./views/fleet/stock_opname/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/stock-opname/create",
        name: "stock-opname-create",
        component: () => import("./views/fleet/stock_opname/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/stock-opname/edit/:id",
        name: "stock-opname-edit",
        component: () => import("./views/fleet/stock_opname/edit.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    // Mapping SO Asset
    {
      path: '/mapping-so',
      name: 'mapping-so',
      component: () => import('./views/fleet/mapping-so/list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // SO Asset
    {
      path: '/so-asset',
      name: 'so-asset',
      component: () => import('./views/fleet/so-asset/list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // List BA SO Asset
    {
      path: '/list-ba-so',
      name: 'list-ba-so',
      component: () => import('./views/fleet/list-ba-so/list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/list-ba-so/viewBA/:id',
      name: 'list-ba-so/viewBA',
      component: () => import('./views/fleet/list-ba-so/viewBa.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },

     // Lap BA SO Asset
     {
      path: '/lap-ba-so',
      name: 'lap-ba-so',
      component: () => import('./views/fleet/lap-ba-so/list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/lap-ba-so/viewLA/:id',
      name: 'lap-ba-so/viewLA',
      component: () => import('./views/fleet/lap-ba-so/viewLa.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // Mapping SO Non Asset
    {
      path: '/mapping-non-asset',
      name: 'mapping-non-asset',
      component: () => import('./views/fleet/mapping-non-asset/list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    //SO Non Asset
    {
      path: '/so-non-asset',
      name: 'so-non-asset',
      component: () => import('./views/fleet/so-non-asset/list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // List BA SO Non Asset
    {
      path: '/list-ba-so-non',
      name: 'list-ba-so-non',
      component: () => import('./views/fleet/list-ba-so-non/list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/list-ba-so-non/viewBA/:id',
      name: 'list-ba-so-non/viewBA',
      component: () => import('./views/fleet/list-ba-so-non/viewBA.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    // Lap hasil SO Non Asset
    {
      path: '/lap-ba-so-non',
      name: 'lap-ba-so-non',
      component: () => import('./views/fleet/lap-ba-so-non/list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/lap-ba-so-non/viewLA/:id',
      name: 'lap-ba-so-non/viewLA',
      component: () => import('./views/fleet/lap-ba-so-non/viewLA.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    {
      path: '/report',
      name: 'report',
      component: () => import('./views/fleet/report/list.vue'),
      meta: {
        requiresAuth: true,
        layout: AdminLayout,
      },
    },
    //stock non aset
    // {
    //     path: "/stock-non-aset",
    //     name: "stock-non-aset",
    //     meta: {
    //         requiresAuth: true,
    //         layout: AdminLayout
    //     },
    //     children: [
    //         {
    //             path: "/stock-non-aset/inbound",
    //             name: "stock-non-aset-inbound",
    //             component: () => import("./views/fleet/stock_non_aset/inbound/index.vue"),
    //             meta: {
    //                 requiresAuth: true,
    //                 layout: AdminLayout
    //             }
    //         }
    //     ]
    // },
    {
        path: "/stock-non-aset/inbound",
        name: "stock-non-aset-inbound",
        component: () => import("./views/fleet/stock_non_aset/inbound/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/stock-non-aset/outbound",
        name: "stock-non-aset-outbound",
        component: () => import("./views/fleet/stock_non_aset/outbound/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/stock-non-aset/stock",
        name: "stock-non-aset-stock",
        component: () => import("./views/fleet/stock_non_aset/stock/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/stock-non-aset/inbound-outbound-recap",
        name: "stock-non-aset-inbound-outbound-recap",
        component: () => import("./views/fleet/stock_non_aset/inbound_outbound_recap/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/waiting-asset-code",
        name: "asset-waiting-asset-code",
        component: () => import("./views/fleet/asset/waiting_asset_code/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset/waiting-asset-code/:id/input",
        name: "asset-waiting-asset-code-input",
        component: () => import("./views/fleet/asset/waiting_asset_code/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset/issued-asset-code",
        name: "asset-issued-asset-code",
        component: () => import("./views/fleet/asset/issued_asset_code/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset/issued-asset-code/:code",
        name: "asset-issued-asset-code-detail",
        component: () => import("./views/fleet/asset/issued_asset_code/detail.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/asset-land",
        name: "asset-asset-land",
        component: () => import("./views/fleet/asset/asset_land/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/asset-land/create",
        name: "asset-asset-land-create",
        component: () => import("./views/fleet/asset/asset_land/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/asset-building",
        name: "asset-asset-building",
        component: () => import("./views/fleet/asset/asset_building/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/asset-building/create",
        name: "asset-asset-building-create",
        component: () => import("./views/fleet/asset/asset_building/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/asset-vehicle",
        name: "asset-asset-vehicle",
        component: () => import("./views/fleet/asset/asset_vehicle/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/asset-vehicle/create",
        name: "asset-asset-vehicle-create",
        component: () => import("./views/fleet/asset/asset_vehicle/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/asset-computer",
        name: "asset-asset-computer",
        component: () => import("./views/fleet/asset/asset_computer/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/asset-computer/create",
        name: "asset-asset-computer-create",
        component: () => import("./views/fleet/asset/asset_computer/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/asset-office-equipment",
        name: "asset-asset-office-equipment",
        component: () => import("./views/fleet/asset/asset_office_equipment/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/asset-office-equipment/create",
        name: "asset-asset-office-equipment/create",
        component: () => import("./views/fleet/asset/asset_office_equipment/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/asset-mechinary",
        name: "asset-asset-mechinary",
        component: () => import("./views/fleet/asset/asset_mechinary/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/asset-mechinary/create",
        name: "asset-asset-mechinary/create",
        component: () => import("./views/fleet/asset/asset_mechinary/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

     {
        path: "/asset/asset-furniture",
        name: "asset-asset-furniture",
        component: () => import("./views/fleet/asset/asset_furniture/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/asset-furniture/create",
        name: "asset-asset-furniture/create",
        component: () => import("./views/fleet/asset/asset_furniture/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset/:type_asset/handover/:id",
        name: "asset-asset-furniture-handover",
        component: () => import("./views/fleet/asset/handover/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset/form-serah-terima-asset",
        name: "asset-form-serah-terima-asset",
        component: () => import("./views/fleet/asset/handover/formSerahTerima.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset/:type_asset/mutasi/:id",
        name: "asset-asset-furniture-mutasi",
        component: () => import("./views/fleet/asset/mutasi/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset/:type_asset/lent/:id",
        name: "asset-asset-furniture-lent",
        component: () => import("./views/fleet/asset/lent/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset/:type_asset/disposal/:id",
        name: "asset-asset-furniture-disposal",
        component: () => import("./views/fleet/asset/disposal/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset/list-hand-over-asset",
        name: "asset-asset-list-hand-over-asset",
        component: () => import("./views/fleet/asset/handover/list.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },

    {
        path: "/asset/list-disposal",
        name: "asset-asset-list-disposal-asset",
        component: () => import("./views/fleet/asset/disposal/list.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset/list-lent-asset",
        name: "asset-asset-list-lent-asset",
        component: () => import("./views/fleet/asset/lent/list.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset/list-mutasi-asset",
        name: "asset-asset-list-mutasi-asset",
        component: () => import("./views/fleet/asset/mutasi/list.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset/asuransi-kendaraan",
        name: "asset-asset-asuransi-kendaraan",
        component: () => import("./views/fleet/asset/asuransi_kendaraan/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/asset/asuransi-kendaraan/create",
        name: "asset-asset-asuransi-kendaraan-create",
        component: () => import("./views/fleet/asset/asuransi_kendaraan/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/create-ba/berita-acara",
        name: "create-ba-berita-acara",
        component: () => import("./views/fleet/berita_acara/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/berita-acara/list-ba/complete",
        name: "berita-acara-list-ba-complete",
        component: () => import("./views/fleet/berita_acara/list.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/berita-acara/hasil-investigasi-biaya/list",
        name: "berita-acara-hasil-investigasi-biaya-list",
        component: () => import("./views/fleet/berita_acara/hasil_investigasi_biaya/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/berita-acara/hasil-investigasi-biaya/:no_ba",
        name: "berita-acara-hasil-investigasi-biaya-create",
        component: () => import("./views/fleet/berita_acara/hasil_investigasi_biaya/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/berita-acara/pengajuan-banding/list",
        name: "berita-acara-pengajuan-banding-list",
        component: () => import("./views/fleet/berita_acara/pengajuan_banding/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/berita-acara/penyesuaian-pembebanan/list",
        name: "berita-acara-penyesuaian-pembebanan-list",
        component: () => import("./views/fleet/berita_acara/penyesuaian_pembebanan/index.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
        path: "/berita-acara/pengajuan-banding/:no_ba",
        name: "berita-acara-pengajuan-banding-create",
        component: () => import("./views/fleet/berita_acara/pengajuan_banding/create.vue"),
        meta: {
            requiresAuth: true,
            layout: AdminLayout
        }
    },
    {
      path: "/gps-setting",
      name: "gps-setting",
      component: () => import("./views/fleet/gps_setting/index.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
    {
      path: "/gps-map",
      name: "gps-map",
      component: () => import("./views/fleet/gps_map/index.vue"),
      meta: {
          requiresAuth: true,
          layout: AdminLayout
      }
    },
  ],
  duplicateNavigationPolicy: 'reload'
})

import Swal from 'sweetalert2';

async function logout(){
  Swal.fire({
    title: 'Your session has expired',
    text: "There is a communications failure. Please re-login again",
    icon: 'warning',
    showCancelButton: false,
    confirmButtonColor: '#3085d6',
    cancelButtonColor: '#d33',
    confirmButtonText: 'Login Again'
  }).then((result) => {
  if (result.isConfirmed) {
    delSession()
    window.location = "/login"
  }else{
    Swal.fire('Deleted Cancel')
  }
  })
}

async function delSession() {
  await store.dispatch("user", null);
  await store.dispatch("permission", null);
}

router.beforeEach((to, from, next) => {
    if (to.matched.some(record => record.meta.requiresAuth)) {
        if (store.getters.user) {
          axios.get("user")
          .then((response) => {
              next();
          })
          .catch((error) => {
            logout();
          })
          return;
        }
        next("/login");
    } else {
        if(['login', 'register'].includes(to.name) && store.getters.user){
          axios.get("user")
          .then((response) => {
            next({ name: 'admin' });
          })
          .catch((error) => {
            logout();
          })
            // next({ name: 'admin' })
            return;
        }
        next();
        // axios.get("user")
        // .then((response) => {
        //     console.log('user logout');
        // })
        // .catch((error) => {
        //   next();
        // })
    }
})

router.onError(error =>{
  if (/loading chunk \d* failed./i.test(error.message) && navigator.onLine) {
      window.location.reload()
  }
}); // onError

export default router
